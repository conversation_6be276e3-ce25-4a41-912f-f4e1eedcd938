# ===========================================
# iOS & macOS Development .gitignore
# ===========================================

# ===========================================
# Xcode & Build Products
# ===========================================
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/*.xcworkspacedata
!*.xcodeproj/xcshareddata/

*.xcworkspace/*
!*.xcworkspace/contents.xcworkspacedata
!*.xcworkspace/xcshareddata/

# Build products
build/
DerivedData/

# Archives
*.xcarchive

# ===========================================
# User-specific Xcode files
# ===========================================
xcuserdata/
*.xcuserstate
*.xcuserdatad
*.xcuserstate
IDEWorkspaceChecks.plist

# ===========================================
# macOS Files
# ===========================================
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# ===========================================
# Dependency Managers
# ===========================================

# CocoaPods
Pods/
*.podspec
!Podfile
!Podfile.lock

# Carthage
Carthage/Checkouts
Carthage/Build
*.framework

# Swift Package Manager
.swiftpm/
Package.resolved
*.xcworkspace

# Accio dependency management
Dependencies/
.accio/

# ===========================================
# Code Coverage & Testing
# ===========================================
*.gcno
*.gcda
*.lcov
coverage/
test_output/
report.xml

# ===========================================
# Fastlane
# ===========================================
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output
fastlane/builds

# ===========================================
# Certificates & Provisioning
# ===========================================
*.p12
*.mobileprovision
*.cer
*.certSigningRequest

# ===========================================
# Debug & Crash Logs
# ===========================================
*.dSYM.zip
*.dSYM
dSYM_upload_result.log

# Crash logs
*.crash

# ===========================================
# Third-party Tools
# ===========================================

# Bugly
buglybin/
cp_buglyQqUploadSymbolLib.jar
cp_buglySymboliOS.jar

# Firebase
GoogleService-Info.plist
google-services.json

# Ruby/Bundler (for fastlane, cocoapods)
.bundle/
vendor/
rvm.env

# Node.js (if using React Native or other tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===========================================
# IDE & Editor Files
# ===========================================

# VSCode
.vscode/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# ===========================================
# Documentation & Generated Files
# ===========================================
docs/generated/
*.hmap
*.ipa

# ===========================================
# Environment & Configuration
# ===========================================
.env
.env.local
.env.development
.env.test
.env.production

# ===========================================
# Temporary Files
# ===========================================
*.tmp
*.temp
*~.nib
*.swp
*.lock

# ===========================================
# Custom Project Files (add your own)
# ===========================================
# Add any project-specific files here
