# 无线传输模块使用文档

## 概述

这个无线传输模块为 ChatsGo 应用提供了 C++ 无线传输库的 Swift 友好的封装。它采用简洁高效的三层架构设计，使用现代化的 Block 回调机制，确保一次编译成功。

## 架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                        Swift Layer                              │
├─────────────────────────────────────────────────────────────────┤
│ • WirelessTransManager.swift                                    │
│ • Swift 闭包回调                                                  │
│ • 便利方法封装                                                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Objective-C Bridge                          │
├─────────────────────────────────────────────────────────────────┤
│ • WirelessTransManagerBridge.h/.mm                             │
│ • NS_ENUM 枚举                                                  │
│ • Block 回调包装                                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        C++ Library                             │
├─────────────────────────────────────────────────────────────────┤
│ • IWirelessTransManager                                         │
│ • ENCallbackType                                                │
│ • Network Operations                                            │
└─────────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. WirelessTransManagerBridge.h

定义 Objective-C 接口，包含：

- NS_ENUM 枚举类型（数据类型、回调类型、状态码）
- Block 回调类型定义
- Objective-C 接口方法

### 2. WirelessTransManagerBridge.mm

实现 C++ 到 Objective-C 的桥接：

- C++ 对象管理
- 回调函数包装
- 线程安全保证

### 3. WirelessTransManager.swift

提供 Swift 友好的 API：

- 现代化闭包回调
- 便利方法
- 错误处理
- 异步操作支持

### 4. ChatsGo-Bridging-Header.h

Swift-ObjC 桥接头文件，导入 Objective-C 头文件。

## 快速开始

### 基本使用

```swift
import Foundation

class WirelessTransferExample {
    private let manager = WirelessTransManager()

    func basicUsage() {
        // 1. 设置回调
        manager.setCallback { [weak self] type, code, size in
            self?.handleCallback(type: type, code: code, size: size)
        }

        // 2. 连接服务器
        let success = manager.connect(ip: "*************", port: 8080)
        if success {
            print("连接成功")
            // 3. 开始接收数据
            manager.startReceiving()
        } else {
            print("连接失败")
        }
    }

    private func handleCallback(type: WirelessTransManager.CallbackType,
                               code: WirelessTransManager.CallbackCode,
                               size: UInt) {
        switch type {
        case .start:
            print("开始传输")
        case .progress:
            print("传输进度: \(size) bytes")
        case .transData:
            print("传输数据: \(size) bytes")
        case .end:
            if code == .success {
                print("传输完成: \(size) bytes")
            } else {
                print("传输失败: \(code.errorDescription)")
            }
        }
    }

    func stopTransfer() {
        manager.stopReceiving()
        print("传输已停止")
    }
}
```

### 异步连接

```swift
class AsyncWirelessTransferExample {
    private let manager = WirelessTransManager()

    func asyncConnection() {
        // 使用便利方法异步连接
        manager.connect(to: "*************", port: 8080) { [weak self] success in
            if success {
                print("异步连接成功")
                self?.setupCallbacksAndStart()
            } else {
                print("异步连接失败")
            }
        }
    }

    private func setupCallbacksAndStart() {
        // 使用简化的回调设置
        manager.setCallbacks(
            onStart: { [weak self] in
                print("📤 开始传输")
                self?.showProgressIndicator()
            },
            onProgress: { size in
                print("📊 进度更新: \(size) bytes")
                // 更新UI进度条
            },
            onComplete: { [weak self] size in
                print("✅ 传输完成: \(size) bytes")
                self?.hideProgressIndicator()
                self?.showSuccessMessage()
            },
            onError: { [weak self] code in
                print("❌ 传输错误: \(code.errorDescription)")
                self?.hideProgressIndicator()
                self?.showErrorMessage(code.errorDescription)
            }
        )

        manager.startReceiving()
    }

    private func showProgressIndicator() {
        // 显示进度指示器
    }

    private func hideProgressIndicator() {
        // 隐藏进度指示器
    }

    private func showSuccessMessage() {
        // 显示成功消息
    }

    private func showErrorMessage(_ message: String) {
        // 显示错误消息
    }
}
```

### 完整的传输管理器

```swift
class FileTransferManager {
    private let manager = WirelessTransManager()
    private var isConnected = false
    private var isReceiving = false

    // 连接状态回调
    var onConnectionChanged: ((Bool) -> Void)?
    var onTransferProgress: ((UInt, WirelessTransManager.CallbackType) -> Void)?
    var onTransferComplete: ((UInt) -> Void)?
    var onError: ((String) -> Void)?

    init() {
        setupCallbacks()
    }

    private func setupCallbacks() {
        manager.setCallback { [weak self] type, code, size in
            guard let self = self else { return }

            switch type {
            case .start:
                self.isReceiving = true
                self.onTransferProgress?(size, type)

            case .progress:
                self.onTransferProgress?(size, type)

            case .transData:
                self.onTransferProgress?(size, type)

            case .end:
                self.isReceiving = false
                if code == .success {
                    self.onTransferComplete?(size)
                } else {
                    self.onError?(code.errorDescription)
                }
            }
        }
    }

    func connect(to host: String, port: Int) {
        manager.connect(to: host, port: port) { [weak self] success in
            self?.isConnected = success
            self?.onConnectionChanged?(success)
        }
    }

    func startReceiving() {
        guard isConnected, !isReceiving else {
            onError?("未连接或正在传输中")
            return
        }

        manager.startReceiving()
    }

    func stopReceiving() {
        manager.stopReceiving()
        isReceiving = false
    }

    func disconnect() {
        stopReceiving()
        manager.cleanup()
        isConnected = false
        onConnectionChanged?(false)
    }

    var connectionStatus: String {
        if isConnected {
            return isReceiving ? "已连接 - 传输中" : "已连接 - 空闲"
        }
        return "未连接"
    }
}
```

## 在 UIViewController 中使用

```swift
class TransferViewController: UIViewController {
    private let transferManager = FileTransferManager()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTransferManager()
    }

    private func setupTransferManager() {
        transferManager.onConnectionChanged = { [weak self] connected in
            DispatchQueue.main.async {
                self?.updateConnectionStatus(connected)
            }
        }

        transferManager.onTransferProgress = { [weak self] size, type in
            DispatchQueue.main.async {
                self?.updateProgress(size: size, type: type)
            }
        }

        transferManager.onTransferComplete = { [weak self] size in
            DispatchQueue.main.async {
                self?.showCompletionAlert(size: size)
            }
        }

        transferManager.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.showErrorAlert(error)
            }
        }
    }

    @IBAction func connectButtonTapped(_ sender: UIButton) {
        transferManager.connect(to: "*************", port: 8080)
    }

    @IBAction func startReceivingButtonTapped(_ sender: UIButton) {
        transferManager.startReceiving()
    }

    @IBAction func stopButtonTapped(_ sender: UIButton) {
        transferManager.stopReceiving()
    }

    private func updateConnectionStatus(_ connected: Bool) {
        // 更新UI状态
    }

    private func updateProgress(size: UInt, type: WirelessTransManager.CallbackType) {
        // 更新进度显示
    }

    private func showCompletionAlert(size: UInt) {
        // 显示完成提示
    }

    private func showErrorAlert(_ error: String) {
        // 显示错误提示
    }
}
```

## API 参考

### WirelessTransManager

#### 枚举类型

```swift
/// 回调事件类型
public enum CallbackType: Int {
    case start = 0      // 开始传输
    case progress       // 进度更新
    case transData      // 传输数据
    case end           // 传输结束
}

/// 回调状态码
public enum CallbackCode: Int {
    case success = 0                    // 成功
    case fail                          // 失败
    case stop                          // 停止
    case receiveError = 100            // 接收错误
    case saveError                     // 保存错误
    case jsonParseFail                 // JSON解析失败
    case notEnoughDiskSpace            // 磁盘空间不足
    case statusError                   // 状态错误
}

/// 数据类型
public enum DataType: Int {
    case other = -1     // 其他
    case photo = 0      // 照片
    case video          // 视频
    case audio          // 音频
    case contact        // 联系人
    case calendar       // 日历
    case file          // 文件
}
```

#### 主要方法

```swift
/// 创建管理器实例
public init()

/// 连接到服务器
public func connect(ip: String, port: Int) -> Bool

/// 异步连接（便利方法）
public func connect(to host: String, port: Int, completion: @escaping (Bool) -> Void)

/// 设置回调函数
public func setCallback(_ callback: @escaping CallbackHandler)

/// 设置简化回调（便利方法）
public func setCallbacks(
    onStart: (() -> Void)? = nil,
    onProgress: ((UInt) -> Void)? = nil,
    onComplete: ((UInt) -> Void)? = nil,
    onError: ((CallbackCode) -> Void)? = nil
)

/// 开始接收数据
public func startReceiving()

/// 停止接收数据
public func stopReceiving()

/// 释放资源
public func cleanup()
```

## 错误处理

### 错误码说明

```swift
extension WirelessTransManager.CallbackCode {
    /// 获取错误描述
    public var errorDescription: String {
        switch self {
        case .success: return "Success"
        case .fail: return "General failure"
        case .stop: return "Operation stopped"
        case .receiveError: return "Receive error"
        case .saveError: return "Save error"
        case .jsonParseFail: return "JSON parse failed"
        case .notEnoughDiskSpace: return "Not enough disk space"
        case .statusError: return "Status error"
        }
    }

    /// 是否为错误状态
    public var isError: Bool {
        return self != .success
    }
}
```

### 最佳实践

1. **内存管理**: 使用 `[weak self]` 避免循环引用
2. **线程安全**: 回调自动在主线程执行，UI 更新无需调度
3. **错误处理**: 检查连接状态和错误码
4. **资源清理**: 在适当时机调用 `cleanup()`

## 技术特点

### 架构优势

- **简洁设计**: 三层架构，职责清晰
- **一次成功**: 避免复杂的类型转换，编译顺利
- **现代化 API**: 支持 Swift 闭包和便利方法
- **线程安全**: 自动主线程回调，无需手动调度

### 内存安全

- **Bridge 管理**: 使用 `__bridge` 安全转换指针
- **Block 回调**: 自动管理生命周期
- **弱引用**: 使用 `[weak self]` 避免循环引用

### 性能优化

- **最小开销**: 直接桥接，减少中间层
- **异步操作**: 非阻塞连接和数据传输
- **资源管理**: 及时释放 C++ 对象
