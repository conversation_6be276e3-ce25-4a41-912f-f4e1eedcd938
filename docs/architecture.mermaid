graph TB
    %% 用户界面层
    subgraph "UI Layer - 用户界面层"
        UI_Home[HomePageController]
        UI_SystemInfo[SystemInfoViewController]
        UI_Transfer[Transfer Module]
        UI_Auth[AuthViewController]
        UI_Chat[ChatViewController]
        UI_Profile[ProfileViewController]
        
        %% Transfer 子模块
        subgraph "Transfer Sub-modules"
            Transfer_BottomSheet[BottomSheetViewController]
            Transfer_Direction[DirectionSelectionBottomSheetView]
            Transfer_DataType[DataTypeSelectionBottomSheetView]
            Transfer_Coordinator[TransferCoordinator]
        end
    end

    %% 业务逻辑层
    subgraph "Business Layer - 业务逻辑层"
        BL_Auth[AuthenticationManager]
        BL_Chat[ChatManager]
        BL_Profile[ProfileManager]
        BL_Network[NetworkManager]
    end

    %% 核心工具层
    subgraph "Core Layer - 核心工具层"
        Core_SystemInfo[SystemInfoManager]
        Core_AppUtility[AppUtility]
        Core_LogManager[LogManager]
        Core_CacheManager[CacheManager]
        Core_KeychainManager[KeychainManager]
    end

    %% 数据访问层
    subgraph "Data Layer - 数据访问层"
        Data_Repository[DataRepository]
        Data_NetworkService[NetworkService]
        Data_LocalStorage[LocalStorageService]
    end

    %% 存储层
    subgraph "Storage Layer - 存储层"
        Storage_CoreData[Core Data]
        Storage_UserDefaults[UserDefaults]
        Storage_Keychain[Keychain]
        Storage_FileSystem[File System]
    end

    %% 系统层
    subgraph "System Layer - 系统层"
        System_UIKit[UIKit Framework]
        System_Foundation[Foundation Framework]
        System_Network[Network Framework]
        System_Darwin[Darwin/Mach APIs]
    end

    %% UI层到业务层的连接
    UI_Home --> BL_Auth
    UI_Home --> BL_Chat
    UI_Home --> Transfer_Coordinator
    UI_SystemInfo --> Core_SystemInfo
    UI_Transfer --> Transfer_Coordinator
    UI_Auth --> BL_Auth
    UI_Chat --> BL_Chat
    UI_Profile --> BL_Profile
    
    %% Transfer 内部连接
    Transfer_Coordinator --> Transfer_BottomSheet
    Transfer_BottomSheet --> Transfer_Direction
    Transfer_BottomSheet --> Transfer_DataType

    %% 业务层到核心工具层的连接
    BL_Auth --> Core_LogManager
    BL_Auth --> Core_KeychainManager
    BL_Chat --> Core_LogManager
    BL_Chat --> BL_Network
    BL_Profile --> Core_LogManager
    BL_Network --> Core_LogManager

    %% 核心工具层到数据层的连接
    Core_SystemInfo --> System_UIKit
    Core_SystemInfo --> System_Foundation
    Core_SystemInfo --> System_Network
    Core_SystemInfo --> System_Darwin
    Core_AppUtility --> System_Foundation
    Core_LogManager --> Data_LocalStorage
    Core_CacheManager --> Data_LocalStorage

    %% 业务层到数据层的连接
    BL_Auth --> Data_Repository
    BL_Chat --> Data_Repository
    BL_Profile --> Data_Repository
    BL_Network --> Data_NetworkService

    %% 数据层到存储层的连接
    Data_Repository --> Storage_CoreData
    Data_Repository --> Storage_UserDefaults
    Data_LocalStorage --> Storage_FileSystem
    Data_NetworkService --> Storage_CoreData
    Core_KeychainManager --> Storage_Keychain

    %% SystemInfoManager 的详细连接
    Core_SystemInfo -.->|电池信息| System_UIKit
    Core_SystemInfo -.->|存储空间| System_Foundation
    Core_SystemInfo -.->|内存信息| System_Darwin
    Core_SystemInfo -.->|网络状态| System_Network
    Core_SystemInfo -.->|设备信息| System_UIKit

    %% 样式定义
    classDef uiLayer fill:#e1f5fe
    classDef businessLayer fill:#f3e5f5
    classDef coreLayer fill:#e8f5e8
    classDef dataLayer fill:#fff3e0
    classDef storageLayer fill:#fce4ec
    classDef systemLayer fill:#f1f8e9

    class UI_Home,UI_SystemInfo,UI_Transfer,UI_Auth,UI_Chat,UI_Profile uiLayer
    class Transfer_BottomSheet,Transfer_Direction,Transfer_DataType,Transfer_Coordinator uiLayer
    class BL_Auth,BL_Chat,BL_Profile,BL_Network businessLayer
    class Core_SystemInfo,Core_AppUtility,Core_LogManager,Core_CacheManager,Core_KeychainManager coreLayer
    class Data_Repository,Data_NetworkService,Data_LocalStorage dataLayer
    class Storage_CoreData,Storage_UserDefaults,Storage_Keychain,Storage_FileSystem storageLayer
    class System_UIKit,System_Foundation,System_Network,System_Darwin systemLayer
