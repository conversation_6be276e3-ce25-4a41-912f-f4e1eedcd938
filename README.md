# ChatsGo iOS 项目

## 项目简介

ChatsGo 是一个基于 iOS 的聊天应用项目，采用 UIKit + MVVM 架构模式开发。

## 项目架构

### 技术栈

- **语言**: Swift 5.0+
- **最低支持版本**: iOS 14.0+
- **UI 框架**: UIKit
- **架构模式**: MVVM
- **布局方式**: Auto Layout (SnapKit)
- **依赖管理**: CocoaPods / Swift Package Manager

### 项目结构

```
ChatsGo/
├── ChatsGo/
│   ├── Application/           # 应用启动相关
│   │   ├── AppDelegate.swift
│   │   └── SceneDelegate.swift
│   ├── Core/                 # 核心功能模块
│   │   └── Foundation/       # 基础工具类
│   │       ├── AppUtility.swift
│   │       ├── LogManager.swift
│   │       └── SystemInfoManager.swift  # 系统信息管理器
│   ├── Home/                 # 主页模块
│   │   ├── HomePageController.swift
│   │   └── SystemInfoViewController.swift
│   └── Assets.xcassets/      # 资源文件
├── ChatsGoTests/             # 单元测试
├── ChatsGoUITests/           # UI测试
├── docs/                     # 项目文档
│   ├── architecture.mermaid  # 系统架构图
│   ├── technical.md         # 技术文档
│   └── status.md            # 项目状态跟踪
└── tasks/                   # 任务管理
    └── tasks.md            # 开发任务列表
```

## 核心功能模块

### 1. 系统信息管理器 (SystemInfoManager)

`SystemInfoManager` 是一个功能完整的系统信息获取工具，提供以下功能：

#### 主要功能

- **电池信息**: 电量百分比、充电状态、低电量模式
- **存储空间**: 总容量、可用空间、使用率统计
- **内存信息**: 物理内存、应用内存使用、可用内存
- **设备信息**: 设备名称、型号、系统版本、设备方向
- **网络状态**: 网络连接状态检测
- **系统信息**: iOS 版本、应用版本、系统运行时间、语言设置

#### 使用方法

```swift
// 获取单例实例
let systemInfo = SystemInfoManager.shared

// 获取电池信息
let batteryInfo = systemInfo.getBatteryInfo()
print("电量: \(batteryInfo.levelPercentage)")
print("状态: \(batteryInfo.stateDescription)")

// 获取存储空间信息
let storageInfo = systemInfo.getStorageInfo()
print("总容量: \(storageInfo.totalSpaceFormatted)")
print("可用空间: \(storageInfo.availableSpaceFormatted)")
print("使用率: \(storageInfo.usagePercentageString)")

// 获取内存信息
let memoryInfo = systemInfo.getMemoryInfo()
print("物理内存: \(memoryInfo.totalPhysicalMemoryFormatted)")
print("应用内存: \(memoryInfo.appMemoryUsageFormatted)")

// 获取设备信息
let deviceInfo = systemInfo.getDeviceInfo()
print("设备型号: \(deviceInfo.model)")
print("设备类型: \(deviceInfo.deviceTypeDescription)")
print("系统版本: \(deviceInfo.systemName) \(deviceInfo.systemVersion)")
print("设备方向: \(deviceInfo.orientationDescription)")

// 获取网络状态 (实时监控)
let networkInfo = systemInfo.getNetworkInfo()
print("网络状态: \(networkInfo.statusDescription)")
print("Wi-Fi 可用: \(networkInfo.isWiFiAvailable ? "是" : "否")")
print("蜂窝网络可用: \(networkInfo.isCellularAvailable ? "是" : "否")")

// 获取系统信息
let systemInfo = systemInfo.getSystemInfo()
print("iOS 版本: \(systemInfo.iOSVersion)")
print("应用版本: \(systemInfo.appFullVersion)")
print("系统运行时间: \(systemInfo.systemUptimeFormatted)")
print("首选语言: \(systemInfo.preferredLanguage)")

// 获取完整系统报告
let report = systemInfo.generateSystemReport()
print("=== 系统信息完整报告 ===")
print(report)

// 获取字典格式的所有信息
let allInfo = systemInfo.getAllSystemInfo()
print("所有信息字典: \(allInfo)")
```

#### 数据模型

##### BatteryInfo

```swift
struct BatteryInfo {
    let level: Float                    // 电量 (0.0-1.0)
    let state: UIDevice.BatteryState    // 电池状态
    let isLowPowerModeEnabled: Bool     // 低电量模式

    var levelPercentage: String         // 电量百分比字符串
    var stateDescription: String        // 状态描述
}
```

##### StorageInfo

```swift
struct StorageInfo {
    let totalSpace: Int64               // 总空间 (字节)
    let availableSpace: Int64           // 可用空间 (字节)
    let usedSpace: Int64               // 已用空间 (字节)

    var totalSpaceFormatted: String     // 格式化总空间
    var availableSpaceFormatted: String // 格式化可用空间
    var usedSpaceFormatted: String      // 格式化已用空间
    var usagePercentage: Float          // 使用率百分比
    var usagePercentageString: String   // 使用率字符串
}
```

##### MemoryInfo

```swift
struct MemoryInfo {
    let totalPhysicalMemory: Int64      // 物理内存总量
    let appMemoryUsage: Int64           // 应用内存使用量
    let availableMemory: Int64          // 可用内存

    var totalPhysicalMemoryFormatted: String
    var appMemoryUsageFormatted: String
    var availableMemoryFormatted: String
}
```

##### DeviceInfo

```swift
struct DeviceInfo {
    let name: String                    // 设备名称
    let model: String                   // 设备型号
    let localizedModel: String          // 本地化型号
    let systemName: String              // 系统名称
    let systemVersion: String           // 系统版本
    let identifierForVendor: String?    // 设备标识符
    let orientation: UIDeviceOrientation // 设备方向
    let userInterfaceIdiom: UIUserInterfaceIdiom // 界面习语

    var deviceTypeDescription: String   // 设备类型描述
    var orientationDescription: String  // 方向描述
}
```

##### NetworkInfo

```swift
struct NetworkInfo {
    let isWiFiAvailable: Bool           // Wi-Fi 网络可用性
    let isCellularAvailable: Bool       // 蜂窝网络可用性
    let isNetworkReachable: Bool        // 网络可达性

    var statusDescription: String       // 网络状态描述 (中文)
}
```

##### SystemInfo

```swift
struct SystemInfo {
    let iOSVersion: String              // iOS 版本号
    let appVersion: String              // 应用版本号
    let appBuild: String                // 应用构建号
    let bundleIdentifier: String        // Bundle 标识符
    let appName: String                 // 应用名称
    let systemUptime: TimeInterval      // 系统运行时间
    let timeZone: TimeZone             // 时区信息
    let preferredLanguage: String       // 首选语言

    var appFullVersion: String          // 完整版本信息
    var systemUptimeFormatted: String   // 格式化运行时间
}
```

#### 特色功能

1. **格式化输出**: 所有数据模型都提供了人性化的格式化输出方法
2. **完整报告**: `generateSystemReport()` 生成格式化的完整系统信息报告
3. **字典汇总**: `getAllSystemInfo()` 返回结构化的字典数据
4. **性能优化**: 使用系统底层 API 获取精确的内存和性能数据
5. **错误处理**: 集成 LogManager 进行错误日志记录
6. **单例设计**: 全局唯一访问，自动管理电池监控状态

#### 架构设计

- **🏗️ 协议导向设计**: 使用 `SystemInfoManagerProtocol` 便于测试和依赖注入
- **⚡ 单例模式**: 确保全局唯一访问和资源管理，自动处理电池监控和网络监听
- **🔄 实时网络监控**: 基于 Network.framework 的 `NWPathMonitor` 实现非阻塞网络检测
- **📊 结构化数据模型**: 使用结构体封装相关数据，提供格式化方法和计算属性
- **🛡️ 完整错误处理**: 集成 LogManager 进行异常捕获和详细日志记录
- **⚡ 高性能实现**: 使用 mach 内核 API 获取精确内存信息，避免阻塞操作
- **🌍 中文本地化**: 状态描述和错误信息全面支持中文显示

#### 核心技术亮点

**1. 实时网络状态监控**

```swift
// 基于 Network.framework 的非阻塞实现
private let pathMonitor: NWPathMonitor
pathMonitor.pathUpdateHandler = { [weak self] path in
    self?.isNetworkReachable = path.status == .satisfied
    self?.isWiFiAvailable = path.usesInterfaceType(.wifi)
    self?.isCellularAvailable = path.usesInterfaceType(.cellular)
}
pathMonitor.start(queue: monitorQueue)
```

**2. 精确内存计算**

```swift
// 使用 mach 内核 API 获取准确的内存使用信息
var info = mach_task_basic_info()
var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO),
          &info, &count)
```

**3. 智能存储分析**

```swift
// 文件系统 API 精确计算可用空间
let resourceValues = try url.resourceValues(forKeys: [
    .volumeTotalCapacityKey,
    .volumeAvailableCapacityForImportantUsageKey
])
```

### 2. 日志管理器 (LogManager)

高性能异步日志系统，支持多目标输出和缓冲优化。

### 3. 应用工具类 (AppUtility)

提供应用信息获取、URL 处理、视图控制器管理等便捷功能。

## 测试

### 单元测试

项目包含完整的单元测试覆盖：

- `SystemInfoManagerTests`: 系统信息管理器功能测试
- 性能测试: 各模块的性能基准测试
- Mock 测试: 数据模型的独立测试

运行测试：

```bash
# 运行所有测试
xcodebuild test -project ChatsGo.xcodeproj -scheme ChatsGo -destination 'platform=iOS Simulator,name=iPhone 14'

# 或在Xcode中使用 Cmd+U
```

## 开发规范

### 代码风格

- 遵循 Swift API 设计指南
- 使用明确的命名，避免歧义
- 适当的访问控制和文档注释
- 遵循 MVVM 架构模式

### 提交规范

```
feat: 添加系统信息管理器
fix: 修复内存泄漏问题
docs: 更新README文档
test: 添加单元测试
refactor: 重构网络层代码
```

## 部署

### 环境要求

- Xcode 14.0+
- iOS 14.0+
- Swift 5.7+

### 构建步骤

1. 克隆项目

```bash
git clone [repository-url]
cd ChatsGo
```

2. 安装依赖 (如果使用 CocoaPods)

```bash
pod install
```

3. 打开项目

```bash
open ChatsGo.xcworkspace
```

4. 选择目标设备并运行

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/新功能`)
3. 提交更改 (`git commit -m 'feat: 添加新功能'`)
4. 推送到分支 (`git push origin feature/新功能`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v1.0.0 (2024-12-19)

#### 🎯 核心功能实现

- ✅ **SystemInfoManager 系统信息管理器** - 完整实现
  - 🔋 电池信息获取 (电量、状态、低电量模式)
  - 💾 存储空间分析 (总容量、可用空间、使用率)
  - 🧠 精确内存计算 (物理内存、应用内存、可用内存)
  - 📱 设备信息检测 (型号、版本、方向、类型)
  - 🌐 **实时网络监控** (Wi-Fi、蜂窝网络、可达性)
  - ⚙️ 系统信息获取 (iOS 版本、应用版本、运行时间)

#### 🛠️ 技术优化

- ⚡ **Network.framework 集成** - 非阻塞实时网络状态监控
- 🏗️ **mach 内核 API** - 精确内存使用情况计算
- 📊 **结构化数据模型** - 完整格式化方法和计算属性
- 🛡️ **完整错误处理** - 集成 LogManager 异常捕获
- 🌍 **中文本地化支持** - 状态描述和错误信息

#### 🔧 基础设施

- ✅ **LogManager 高性能日志系统** - 异步缓冲、多目标输出
- ✅ **AppUtility 应用工具类** - 信息获取、URL 处理、视图管理
- ✅ **完善单元测试覆盖** - 功能测试、性能基准、Mock 测试
- ✅ **项目文档和规范** - 架构图、技术文档、开发规范

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](repository-url/issues)
- 邮箱: [项目邮箱]

---

**注意**: 本项目仍在开发中，部分功能可能会发生变化。请关注项目状态文档获取最新进展。
