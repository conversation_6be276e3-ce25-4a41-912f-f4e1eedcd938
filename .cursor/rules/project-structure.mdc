---
description: "用于生成和维护 UIKit 项目结构的的规则"
alwaysApply: true
---

# UIKit 项目结构

在创建新文件或功能时，请遵循以下目录结构：

* **Application:** 包含 `AppDelegate` 和 `SceneDelegate` 文件。
* **Common:** 可复用的 `UIView` 子类、`UIViewController` 扩展和工具类。
* **Features:** 应用的每个功能模块都位于其自己的子目录中。
    * **[FeatureName]**
        * **Controller:** `UIViewController` 子类。
        * **View:** 特定于该功能的自定义 `UIView` 子类。
        * **Model:** 特定于该功能的数据模型。
        * **(可选) ViewModel:** 如果使用 MVVM 模式，则存放 ViewModels。
* **Services:** 网络、数据持久化和其他应用级服务。
* **Resources:** 资产、字体和其他非代码资源。

在创建新文件之前，始终询问它属于哪个功能，并将其放置在相应的目录中。
