---
description: "用于在 Controller 或 ViewModel 中管理数据和逻辑的规则"
globs: ["**/Controller/**/*.swift", "**/ViewModel/**/*.swift"]
---

# 数据和逻辑管理

在实现业务逻辑时：

* **MVC 模式:**
    * `UIViewController` 作为控制器。它负责用用户输入更新模型，并根据模型变化更新视图。
    * 即使在 ViewController 内部，也要保持视图相关代码（`UIView` 配置）和模型交互逻辑的分离。
* **MVVM 模式:**
    * 如果使用 MVVM，`ViewModel` 应该是一个 `class` 并且不应该导入 UIKit。
    * `ViewModel` 从 `ViewController` 接收输入，并通过闭包或响应式框架（如 Combine 的 `@Published`）提供输出。
    * `ViewController` 负责观察 `ViewModel` 并相应地更新 `UIView` 组件。
* **依赖注入:** 通过构造函数将任何必要的服务（例如网络服务）注入到控制器或视图模型中，以方便测试和解耦。
