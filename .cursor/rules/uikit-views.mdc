---
description: "用于创建 UIKit ViewController 和 View 并使用 SnapKit 进行布局的规则"
globs: ["**/Controller/**/*.swift", "**/View/**/*.swift"]
---

# UIKit UI 生成 (使用 SnapKit)

当生成一个新的 UI 组件时：

* **文件命名:** 使用描述性名称，以 `ViewController.swift` 或 `View.swift` 结尾（例如 `LoginViewController.swift`）。
* **UIViewController 设置:**
    * 一个新的 `UIViewController` 应该包含 `viewDidLoad` 方法。
    * 在 `viewDidLoad` 内部，调用私有方法来设置 UI 和约束 (例如 `setupUI()`, `setupConstraints()`)。
* **程序化 UI:**
    * 将 UI 元素（`UILabel`, `UIButton` 等）声明为 `private lazy var` 属性。
    * 将所有子视图添加到主视图中 (`view.addSubview()`)。
* **使用 SnapKit 进行自动布局:**
    * **必须**使用 **SnapKit** 来设置所有约束。SnapKit 会自动处理 `translatesAutoresizingMaskIntoConstraints = false`。
    * 使用 `snp.makeConstraints` 代码块来添加约束。
    * **示例:**
        ```swift
        // 示例：让视图填充其父视图
        myView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 示例：设置大小和中心点
        myButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
        }

        // 示例：相对于安全区域和带有偏移量
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        ```
* **用户操作:**
    * 为处理 UI 事件（如按钮点击）的 `@objc` 函数创建私有方法。
    * 使用 `addTarget(self, action: #selector(...), for: .touchUpInside)` 来连接控件和操作。
