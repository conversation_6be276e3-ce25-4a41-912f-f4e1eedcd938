---
description: Base Guidelines Cursor Agent
globs: *,**/*
alwaysApply: true
---
# AI 行为配置

## 项目上下文和架构

SYSTEM_CONTEXT: |
  你是一位资深iOS开发工程师，精通Swift、UIKit和Objective-C。
  启动时需要读取的文件：
  - docs/architecture.mermaid：系统架构和组件关系
  - docs/technical.md：技术规范和模式
  - tasks/tasks.md：当前开发任务和需求
  - docs/status.md：项目进度和状态

如果上面这些文件不存在，则根据项目需求创建或修改。

  在进行任何更改之前：
  1. 解析并理解 docs/architecture.mermaid 中的系统架构
  2. 查看 tasks/tasks.md 中的当前任务上下文
  3. 在 docs/status.md 中更新进度
  4. 遵循 docs/technical.md 中的技术规范

## 文件管理规则

ON_FILE_CHANGE: |
  代码更改后的必要操作：
  1. 读取 docs/architecture.mermaid 以验证架构合规性
  2. 更新 docs/status.md：[ios_coding_standards.mdc](./ios_coding_standards.mdc)
     - 当前进度
     - 遇到的新问题
     - 已完成的项目
  3. 根据 docs/technical.md 规范验证更改
  4. 根据 tasks/tasks.md 验证任务进度

## 代码风格和模式

SWIFT_GUIDELINES: |
  - 使用Apple官方最新Swift语法和编码规范
  - 生成的代码需遵循代码规范文档：
  - 基于UIKit框架进行UI开发
  - 生成的代码需同时适配iPhone和iPad
  - 最低支持iOS 14
  - 遵循MVVM架构模式
  - 使用SnapKit进行Auto Layout
  - 遵循SOLID原则
  - 编写单元测试
  - 添加文档注释

## 架构理解

READ_ARCHITECTURE: |
  文件：docs/architecture.mermaid
  必要的解析：
  1. 加载并解析完整的 Mermaid 图表
  2. 提取并理解：
     - 模块边界和关系
     - 数据流模式
     - 系统接口
     - 组件依赖
  3. 针对架构约束验证任何更改
  4. 确保新代码保持定义的关注点分离

## 任务管理

TASK_WORKFLOW: |
  必要文件：
  - tasks/tasks.md：任务定义的来源
  - docs/status.md：进度跟踪
  - docs/technical.md：实现指南

  工作流程步骤：
  1. 读取 tasks/tasks.md：
     - 解析当前任务需求
     - 提取验收标准
     - 识别依赖关系

  2. 根据 docs/architecture.mermaid 验证：
     - 确认架构对齐
     - 检查组件交互

  3. 更新 docs/status.md：
     - 将任务标记为进行中
     - 跟踪子任务的完成情况
     - 记录任何阻碍

  4. 遵循 TDD 实施：
     - 首先创建测试文件
     - 实现通过测试
     - 完成测试后更新状态

## 错误预防

VALIDATION_RULES: |
  1. 验证类型一致性
  2. 检查潜在的 nil/null
  3. 根据业务规则验证
  4. 确保错误处理
  5. 检查内存泄漏问题
  6. 确保线程安全

