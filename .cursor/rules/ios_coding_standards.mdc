---
description: 
globs: *.swift
alwaysApply: false
---
# iOS 代码规范

## 📋 目录

- [Swift 语言规范](mdc:#-swift-语言规范)
- [Swift API 设计原则](mdc:#-swift-api-设计原则)
- [命名规范](mdc:#-命名规范)
- [代码组织](mdc:#-代码组织)
- [UIKit 开发规范](mdc:#-uikit-开发规范)
- [MVVM 架构规范](mdc:#-mvvm-架构规范)
- [内存管理](mdc:#-内存管理)
- [错误处理](mdc:#-错误处理)
- [网络与数据](mdc:#-网络与数据)
- [性能优化](mdc:#-性能优化)
- [安全规范](mdc:#-安全规范)
- [代码注释规范](mdc:#-代码注释规范)
- [测试规范](mdc:#-测试规范)
- [依赖管理](mdc:#-依赖管理)
- [代码检查清单](mdc:#-代码检查清单)

---

## 🎯 Swift 语言规范

### 基础语法

#### 可选值处理

```swift
// ✅ 推荐
guard let user = currentUser else { return }

if let email = user.email {
    sendEmail(to: email)
}

let displayName = user.name ?? "匿名用户"

// ❌ 避免 - 强制解包和类型转换
let email = user.email!
let name = user.name as! String
```

#### 避免歧义的命名

```swift
// ✅ 推荐 - 包含所有避免歧义的单词
extension List {
    public mutating func remove(at position: Index) -> Element
}
employees.remove(at: x) // 清晰表明是按位置删除

// ❌ 避免 - 可能引起歧义
employees.remove(x) // 不清楚：是删除 x 还是在位置 x 删除？

// ✅ 推荐 - 省略不必要的单词
public mutating func remove(_ member: Element) -> Element?
allViews.remove(cancelButton) // Element 信息是多余的

// ❌ 避免 - 重复类型信息
public mutating func removeElement(_ member: Element) -> Element?
allViews.removeElement(cancelButton)
```

#### 类型推断

```swift
// ✅ 推荐
let items = [String]()
let userInfo = ["name": "张三", "age": "25"]

// ❌ 避免
let items: [String] = [String]()
let userInfo: [String: String] = ["name": "张三", "age": "25"]
```

#### 访问控制和方法组织

```swift
// ✅ 推荐 - 合理使用访问控制
class UserManager {
    private let networkService: NetworkServiceProtocol
    private var cachedUsers: [User] = []

    internal func fetchUsers() { /* ... */ }
    public func getCurrentUser() -> User? { /* ... */ }
}

// ✅ 推荐 - 文件内私有
fileprivate func helperFunction() { /* ... */ }

// ✅ 推荐 - 方法共享基础名称（相同含义或不同域）
extension Shape {
    func contains(_ other: Point) -> Bool { /* ... */ }
    func contains(_ other: Shape) -> Bool { /* ... */ }
    func contains(_ other: LineSegment) -> Bool { /* ... */ }
}

// ✅ 推荐 - 几何类型和集合是不同域，可以共享方法名
extension Collection where Element: Equatable {
    func contains(_ sought: Element) -> Bool { /* ... */ }
}

// ❌ 避免 - 不同语义的方法使用相同名称
extension Database {
    func index() { /* 重建搜索索引 */ }
    func index(_ n: Int, inTable: TableID) -> TableRow { /* 返回第n行 */ }
}
// 应该命名为: rebuildSearchIndex() 和 row(_:inTable:)
```

### 函数和闭包

#### 函数定义和参数

```swift
// ✅ 推荐 - 参数名服务于文档
func filter(_ predicate: (Element) -> Bool) -> [Element]
func replaceRange(_ subRange: Range, with newElements: [Element])

// ✅ 推荐 - 使用默认参数简化常用场景
func compare(_ other: String,
            options: CompareOptions = [],
            range: Range? = nil,
            locale: Locale? = nil) -> Ordering

// ✅ 推荐 - 默认参数放在参数列表末尾
func fetchUser(by id: String,
              includeProfile: Bool = true,
              completion: @escaping (Result<User, Error>) -> Void) {
    // 实现
}

// ✅ 推荐 - 简化闭包语法
users.filter { $0.isActive }
     .sorted { $0.name < $1.name }
     .map { $0.displayName }

// ✅ 推荐 - 闭包参数和元组成员要有标签
func ensureUniqueStorage(
    minimumCapacity requestedCapacity: Int,
    allocate: (_ byteCount: Int) -> UnsafePointer<Void>
) -> (reallocated: Bool, capacityChanged: Bool)
```

#### 尾随闭包

```swift
// ✅ 推荐
UIView.animate(withDuration: 0.3) {
    self.view.alpha = 0.5
}

// ❌ 避免（当闭包是唯一参数时）
UIView.animate(withDuration: 0.3, animations: {
    self.view.alpha = 0.5
})
```

---

## 🎯 Swift API 设计原则

### 清晰使用原则

#### 包含必要单词避免歧义

```swift
// ✅ 推荐 - 明确操作意图
extension List {
    public mutating func remove(at position: Index) -> Element
}
employees.remove(at: x) // 清楚是按位置删除

// ❌ 避免 - 可能引起歧义
employees.remove(x) // 不清楚是删除x还是在位置x删除
```

#### 省略多余单词

```swift
// ✅ 推荐 - 省略重复的类型信息
public mutating func remove(_ member: Element) -> Element?
allViews.remove(cancelButton)

// ❌ 避免 - 不必要的重复
public mutating func removeElement(_ member: Element) -> Element?
allViews.removeElement(cancelButton)
```

#### 根据角色命名而非类型

```swift
// ✅ 推荐 - 体现变量角色
var greeting = "Hello"
class ProductionLine {
    func restock(from supplier: WidgetFactory)
}

// ❌ 避免 - 仅重复类型信息
var string = "Hello"
class ProductionLine {
    func restock(from widgetFactory: WidgetFactory)
}
```

### 流畅使用原则

#### 弱类型参数需要角色描述

```swift
// ✅ 推荐 - 为弱类型参数添加名词
func addObserver(_ observer: NSObject, forKeyPath path: String)
grid.addObserver(self, forKeyPath: graphics) // 清晰

// ❌ 避免 - 弱类型参数缺乏描述
func add(_ observer: NSObject, for keyPath: String)
grid.add(self, for: graphics) // 模糊
```

#### 优先使用默认参数

```swift
// ✅ 推荐 - 使用默认参数简化常用情况
extension String {
    public func compare(
        _ other: String,
        options: CompareOptions = [],
        range: Range? = nil,
        locale: Locale? = nil
    ) -> Ordering
}

let order = lastName.compare(royalFamilyName) // 简洁

// ❌ 避免 - 方法族增加认知负担
// compare(_:)
// compare(_:options:)
// compare(_:options:range:)
// compare(_:options:range:locale:)
```

---

## 📝 命名规范

### 类和结构体

```swift
// ✅ 推荐 - 使用大驼峰命名
class LoginViewController: UIViewController { }
struct UserProfile { }
enum NetworkError { }
protocol AuthenticationServiceProtocol { }

// ❌ 避免 - 不使用前缀（Swift 项目）
class FMLoginViewController { }
```

### 变量和函数

```swift
// ✅ 推荐 - 遵循角色命名，而非类型命名
var greeting = "Hello" // 而非 var string = "Hello"
let maxRetryCount: Int = 3

// ✅ 推荐 - 函数命名要促进清晰使用
func validateUserInput() -> Bool { }
func fetchUserProfile(for userID: String) { }  // 使用介词标签
func remove(at index: Int) -> Element          // 避免歧义

// ✅ 推荐 - 布尔值使用 is/has/can/should 前缀
var isLoading: Bool = false
var hasNetworkConnection: Bool = true
var canEditProfile: Bool = false
var shouldShowAlert: Bool = true

// ✅ 推荐 - 弱类型参数需要角色描述
func addObserver(_ observer: NSObject, forKeyPath path: String)
// 而非: func add(_ observer: NSObject, for keyPath: String)
```

### 常量和枚举

```swift
// ✅ 推荐 - 常量命名体现用途
private let animationDuration: TimeInterval = 0.3
private let maxUsernameLength: Int = 20

// ✅ 推荐 - 枚举使用 lowerCamelCase
enum UserStatus {
    case active
    case inactive
    case suspended
}

// ✅ 推荐 - 关联值使用描述性标签
enum APIEndpoint {
    case login
    case userProfile(userID: String)
    case logout
}

// ✅ 推荐 - 缩写词遵循大小写约定
var utf8Bytes: [UTF8.CodeUnit]
var isRepresentableAsASCII = true
var userSMTPServer: SecureSMTPServer

// 普通缩写词当作普通单词处理
var radarDetector: RadarScanner
var enjoysScubaDiving = true
```

---

## 📁 代码组织

### ViewController 分区规范

```swift
class LoginViewController: UIViewController {

    // MARK: - Properties
    private var viewModel: LoginViewModel!
    private let disposeBag = DisposeBag()

    private lazy var emailTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "邮箱"
        textField.keyboardType = .emailAddress
        textField.autocapitalizationType = .none
        return textField
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        bindViewModel()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 页面即将显示时的逻辑
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "登录"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
    }

    private func setupConstraints() {
        // SnapKit 约束设置
    }

    private func bindViewModel() {
        // ViewModel 数据绑定
    }

    // MARK: - Actions
    @objc private func loginButtonTapped() {
        viewModel.login()
    }

    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    // MARK: - Private Methods
    private func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func updateLoginButtonState() {
        loginButton.isEnabled = viewModel.isFormValid
    }
}

// MARK: - UITextFieldDelegate
extension LoginViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }
}
```

### 文件组织结构

```
LoginModule/
├── Views/
│   ├── LoginViewController.swift
│   ├── LoginView.swift
│   └── Components/
│       └── LoginFormView.swift
├── ViewModels/
│   └── LoginViewModel.swift
├── Models/
│   ├── User.swift
│   └── LoginRequest.swift
├── Services/
│   ├── AuthenticationService.swift
│   └── Protocols/
│       └── AuthenticationServiceProtocol.swift
└── Resources/
    └── Login.storyboard
```

---

## 🎨 UIKit 开发规范

### 视图创建

```swift
// ✅ 推荐 - 使用 lazy var 创建视图
private lazy var titleLabel: UILabel = {
    let label = UILabel()
    label.font = .systemFont(ofSize: 24, weight: .bold)
    label.textColor = .label
    label.numberOfLines = 0
    label.textAlignment = .center
    return label
}()

// ✅ 推荐 - 复杂视图使用工厂方法
private func createLoginButton() -> UIButton {
    let button = UIButton(type: .system)
    button.setTitle("登录", for: .normal)
    button.setTitleColor(.white, for: .normal)
    button.backgroundColor = .systemBlue
    button.layer.cornerRadius = 8
    button.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
    return button
}
```

### Auto Layout 约束

```swift
// ✅ 推荐 - 使用 SnapKit
private func setupConstraints() {
    titleLabel.snp.makeConstraints { make in
        make.top.equalTo(view.safeAreaLayoutGuide).offset(50)
        make.centerX.equalToSuperview()
        make.leading.trailing.equalToSuperview().inset(20)
    }

    emailTextField.snp.makeConstraints { make in
        make.top.equalTo(titleLabel.snp.bottom).offset(40)
        make.leading.trailing.equalToSuperview().inset(20)
        make.height.equalTo(44)
    }
}

// ❌ 避免 - 不要混用约束方式
// 不要同时使用 SnapKit 和 NSLayoutConstraint
```

### 主线程规范

```swift
// ✅ 推荐 - UI 更新必须在主线程
private func updateUI(with user: User) {
    DispatchQueue.main.async { [weak self] in
        self?.nameLabel.text = user.name
        self?.avatarImageView.image = user.avatar
        self?.activityIndicator.stopAnimating()
    }
}

// ✅ 推荐 - 使用 @MainActor（iOS 15+）
@MainActor
private func updateUserInterface() {
    // UI 更新代码
}
```

---

## 🏗️ MVVM 架构规范

### ViewModel 设计

```swift
// ✅ 推荐 - ViewModel 职责清晰
class LoginViewModel {

    // MARK: - Input Properties
    var email: String = "" {
        didSet { validateForm() }
    }

    var password: String = "" {
        didSet { validateForm() }
    }

    // MARK: - Output Properties
    private(set) var isFormValid: Bool = false {
        didSet {
            formValidationHandler?(isFormValid)
        }
    }

    private(set) var isLoading: Bool = false {
        didSet {
            loadingStateHandler?(isLoading)
        }
    }

    // MARK: - Callbacks
    var formValidationHandler: ((Bool) -> Void)?
    var loginSuccessHandler: ((User) -> Void)?
    var errorHandler: ((String) -> Void)?
    var loadingStateHandler: ((Bool) -> Void)?

    // MARK: - Dependencies
    private let authService: AuthenticationServiceProtocol
    private let validator: InputValidatorProtocol

    // MARK: - Initialization
    init(authService: AuthenticationServiceProtocol = AuthenticationService(),
         validator: InputValidatorProtocol = InputValidator()) {
        self.authService = authService
        self.validator = validator
    }

    // MARK: - Public Methods
    func login() {
        guard isFormValid else {
            errorHandler?("请检查输入信息")
            return
        }

        isLoading = true

        authService.login(email: email, password: password) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false

                switch result {
                case .success(let user):
                    self?.loginSuccessHandler?(user)
                case .failure(let error):
                    self?.errorHandler?(error.localizedDescription)
                }
            }
        }
    }

    // MARK: - Private Methods
    private func validateForm() {
        isFormValid = validator.isValidEmail(email) &&
                     validator.isValidPassword(password)
    }
}
```

### View-ViewModel 绑定

```swift
// ✅ 推荐 - 清晰的数据绑定
private func bindViewModel() {
    // 输入绑定
    emailTextField.addTarget(self, action: #selector(emailTextChanged), for: .editingChanged)
    passwordTextField.addTarget(self, action: #selector(passwordTextChanged), for: .editingChanged)

    // 输出绑定
    viewModel.formValidationHandler = { [weak self] isValid in
        self?.loginButton.isEnabled = isValid
        self?.loginButton.alpha = isValid ? 1.0 : 0.5
    }

    viewModel.loadingStateHandler = { [weak self] isLoading in
        if isLoading {
            self?.activityIndicator.startAnimating()
            self?.loginButton.setTitle("", for: .normal)
        } else {
            self?.activityIndicator.stopAnimating()
            self?.loginButton.setTitle("登录", for: .normal)
        }
    }

    viewModel.loginSuccessHandler = { [weak self] user in
        self?.navigationController?.popViewController(animated: true)
    }

    viewModel.errorHandler = { [weak self] message in
        self?.showErrorAlert(message)
    }
}

@objc private func emailTextChanged() {
    viewModel.email = emailTextField.text ?? ""
}

@objc private func passwordTextChanged() {
    viewModel.password = passwordTextField.text ?? ""
}
```

---

## 🧠 内存管理

### 弱引用使用

```swift
// ✅ 推荐 - 避免循环引用
class UserProfileViewController: UIViewController {
    private var viewModel: UserProfileViewModel?

    override func viewDidLoad() {
        super.viewDidLoad()

        viewModel?.dataUpdateHandler = { [weak self] user in
            self?.updateUI(with: user)
        }

        // 网络请求
        NetworkManager.shared.fetchUserProfile { [weak self] result in
            DispatchQueue.main.async {
                self?.handleProfileResponse(result)
            }
        }
    }
}

// ✅ 推荐 - Timer 使用
class CountdownView: UIView {
    private weak var timer: Timer?

    func startCountdown() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateCountdown()
        }
    }

    deinit {
        timer?.invalidate()
    }
}
```

### 资源释放

```swift
// ✅ 推荐 - 及时释放资源
class VideoPlayerViewController: UIViewController {
    private var player: AVPlayer?
    private var playerObserver: Any?

    deinit {
        if let observer = playerObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        player?.pause()
        player = nil
    }
}
```

---

## ⚠️ 错误处理

### Result 类型使用

```swift
// ✅ 推荐 - 使用 Result 类型
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case serverError(Int)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .serverError(let code):
            return "服务器错误: \(code)"
        }
    }
}

func fetchData(completion: @escaping (Result<DataModel, NetworkError>) -> Void) {
    // 网络请求实现
}

// 使用
fetchData { result in
    switch result {
    case .success(let data):
        // 处理成功情况
    case .failure(let error):
        print("Error: \(error.localizedDescription)")
    }
}
```

### Guard 语句使用

```swift
// ✅ 推荐 - 早期返回
func processUserData(_ userData: [String: Any]) {
    guard let userId = userData["id"] as? String,
          !userId.isEmpty else {
        logger.error("Invalid user ID")
        return
    }

    guard let email = userData["email"] as? String,
          isValidEmail(email) else {
        logger.error("Invalid email format")
        return
    }

    // 正常处理逻辑
    processValidUserData(userId: userId, email: email)
}
```

---

## 🌐 网络与数据

### 网络请求

```swift
// ✅ 推荐 - 网络服务设计
protocol NetworkServiceProtocol {
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        responseType: T.Type,
        completion: @escaping (Result<T, NetworkError>) -> Void
    )
}

class NetworkService: NetworkServiceProtocol {
    private let session: URLSession
    private let baseURL: String

    init(baseURL: String = "https://api.followme.com") {
        self.baseURL = baseURL

        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        self.session = URLSession(configuration: configuration)
    }

    func request<T: Decodable>(
        endpoint: APIEndpoint,
        responseType: T.Type,
        completion: @escaping (Result<T, NetworkError>) -> Void
    ) {
        guard let url = endpoint.url(baseURL: baseURL) else {
            completion(.failure(.invalidURL))
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = endpoint.httpMethod
        request.allHTTPHeaderFields = endpoint.headers
        request.httpBody = endpoint.body

        session.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(.networkError(error)))
                return
            }

            guard let httpResponse = response as? HTTPURLResponse,
                  200...299 ~= httpResponse.statusCode else {
                completion(.failure(.serverError((response as? HTTPURLResponse)?.statusCode ?? 0)))
                return
            }

            guard let data = data else {
                completion(.failure(.noData))
                return
            }

            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let result = try decoder.decode(T.self, from: data)
                completion(.success(result))
            } catch {
                completion(.failure(.decodingError(error)))
            }
        }.resume()
    }
}
```

### 数据模型和多态性处理

```swift
// ✅ 推荐 - Codable 模型设计
struct User: Codable {
    let id: String
    let name: String
    let email: String
    let avatar: URL?
    let createdAt: Date
    let isActive: Bool

    private enum CodingKeys: String, CodingKey {
        case id
        case name
        case email
        case avatar = "avatar_url"
        case createdAt = "created_at"
        case isActive = "is_active"
    }
}

// ✅ 推荐 - 计算属性
extension User {
    var displayName: String {
        return name.isEmpty ? "匿名用户" : name
    }

    var isEmailVerified: Bool {
        return !email.isEmpty && email.contains("@")
    }
}

// ✅ 推荐 - 处理无约束多态性，避免重载歧义
struct Array {
    /// 在 `self.endIndex` 处插入 `newElement`
    public mutating func append(_ newElement: Element)

    /// 在 `self.endIndex` 处按顺序插入 `newElements` 的内容
    public mutating func append(contentsOf newElements: S)
        where S.Iterator.Element == Element
}

// 这样避免了当 Element 是 Any 时的歧义：
// values.append([2, 3, 4]) // 现在明确是添加数组内容而非数组本身
```

---

## ⚡ 性能优化

### 图片处理

```swift
// ✅ 推荐 - 异步图片加载
extension UIImageView {
    func loadImage(from url: URL, placeholder: UIImage? = nil) {
        self.image = placeholder

        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let data = data,
                  let image = UIImage(data: data) else {
                return
            }

            DispatchQueue.main.async {
                self?.image = image
            }
        }.resume()
    }
}
```

### 集合操作优化

```swift
// ✅ 推荐 - 高效的集合操作
let activeUsers = users.lazy
    .filter { $0.isActive }
    .prefix(10)
    .map { $0.displayName }

// ✅ 推荐 - 使用 Set 进行快速查找
let blockedUserIds = Set(blockedUsers.map { $0.id })
let filteredUsers = allUsers.filter { !blockedUserIds.contains($0.id) }
```

---

## 🔒 安全规范

### 敏感数据处理

```swift
// ✅ 推荐 - 使用 Keychain 存储敏感数据
class KeychainManager {
    private let service = "com.followme.app"

    func save(token: String, for account: String) throws {
        let data = token.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecValueData as String: data
        ]

        SecItemDelete(query as CFDictionary)

        let status = SecItemAdd(query as CFDictionary, nil)
        guard status == errSecSuccess else {
            throw KeychainError.saveError
        }
    }

    func load(account: String) throws -> String {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        guard status == errSecSuccess,
              let data = dataTypeRef as? Data,
              let token = String(data: data, encoding: .utf8) else {
            throw KeychainError.loadError
        }

        return token
    }
}

// ❌ 避免 - 不要在 UserDefaults 中存储敏感信息
// UserDefaults.standard.set(password, forKey: "user_password") // 错误！
```

### 网络安全

```swift
// ✅ 推荐 - HTTPS 和证书验证
class SecureNetworkService {
    private lazy var session: URLSession = {
        let configuration = URLSessionConfiguration.default
        configuration.urlCache = nil
        configuration.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        return URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }()
}

extension SecureNetworkService: URLSessionDelegate {
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {

        // 实施证书固定等安全措施
        guard let serverTrust = challenge.protectionSpace.serverTrust else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }

        completionHandler(.useCredential, URLCredential(trust: serverTrust))
    }
}
```

---

## �� 代码注释规范

### 文档注释和复杂度标注

````swift
/**
 用户认证服务，负责处理登录、注册和令牌管理

 该类提供了完整的用户认证功能，包括：
 - 用户登录验证
 - 新用户注册
 - 访问令牌管理
 - 登录状态持久化

 - Important: 所有网络请求都在后台线程执行，回调在主线程
 - Note: 令牌会自动存储在 Keychain 中
 - Warning: 请确保在使用前检查网络连接状态
 */
class AuthenticationService {

    /**
     使用邮箱和密码登录

     - Parameters:
        - email: 用户邮箱地址
        - password: 用户密码
        - completion: 登录结果回调，在主线程执行

     - Returns: 无返回值，结果通过 completion 回调

     - Throws: 不会抛出异常，错误通过 Result 类型传递

     ```swift
     authService.login(email: "<EMAIL>", password: "password") { result in
         switch result {
         case .success(let user):
             print("登录成功: \(user.name)")
         case .failure(let error):
             print("登录失败: \(error.localizedDescription)")
         }
     }
     ```
     */
    func login(email: String, password: String, completion: @escaping (Result<User, AuthError>) -> Void) {
        // 实现
    }

    /**
     计算用户的信誉分数

     - Complexity: O(n²) 其中 n 是用户活动数量
     - Note: 由于复杂的算法，大量数据时可能较慢
     */
    var reputationScore: Double {
        // 复杂计算逻辑
        return 0.0
    }
}

// ✅ 推荐 - 使用自由函数的场景
// 1. 没有明显的 self
min(x, y, z)

// 2. 无约束泛型函数
print(x)

// 3. 函数语法是既定域记号的一部分
sin(x)
````

### 行内注释

```swift
class NetworkManager {

    func fetchData() {
        // TODO: 添加缓存机制以提高性能

        // FIXME: 需要处理网络超时的情况

        // MARK: - 这里开始处理数据解析

        // NOTE: 这个方法会在后台线程执行
        DispatchQueue.global(qos: .background).async {
            // 复杂的数据处理逻辑
        }
    }
}
```

---

## 🧪 测试规范

### 单元测试

```swift
import XCTest
@testable import FollowMe

class LoginViewModelTests: XCTestCase {

    var viewModel: LoginViewModel!
    var mockAuthService: MockAuthenticationService!

    override func setUp() {
        super.setUp()
        mockAuthService = MockAuthenticationService()
        viewModel = LoginViewModel(authService: mockAuthService)
    }

    override func tearDown() {
        viewModel = nil
        mockAuthService = nil
        super.tearDown()
    }

    func testValidEmailAndPassword_ShouldEnableLogin() {
        // Given
        viewModel.email = "<EMAIL>"
        viewModel.password = "validPassword123"

        // When
        // ViewModel 内部会自动验证

        // Then
        XCTAssertTrue(viewModel.isFormValid)
    }

    func testInvalidEmail_ShouldDisableLogin() {
        // Given
        viewModel.email = "invalid-email"
        viewModel.password = "validPassword123"

        // When
        // ViewModel 内部会自动验证

        // Then
        XCTAssertFalse(viewModel.isFormValid)
    }

    func testSuccessfulLogin_ShouldCallSuccessHandler() {
        // Given
        let expectation = XCTestExpectation(description: "Login success")
        let expectedUser = User(id: "123", name: "Test User", email: "<EMAIL>")
        mockAuthService.loginResult = .success(expectedUser)

        viewModel.loginSuccessHandler = { user in
            XCTAssertEqual(user.id, expectedUser.id)
            expectation.fulfill()
        }

        viewModel.email = "<EMAIL>"
        viewModel.password = "password"

        // When
        viewModel.login()

        // Then
        wait(for: [expectation], timeout: 1.0)
    }
}
```

---

## 📦 依赖管理

### CocoaPods 规范

```ruby
# Podfile
platform :ios, '13.0'
use_frameworks!

target 'FollowMe' do
  # UI 框架
  pod 'SnapKit', '~> 5.0'

  # 网络
  pod 'Alamofire', '~> 5.0'

  # 图片加载
  pod 'Kingfisher', '~> 7.0'

  # 响应式编程
  pod 'RxSwift', '~> 6.0'
  pod 'RxCocoa', '~> 6.0'

  target 'FollowMeTests' do
    inherit! :search_paths
    pod 'RxTest', '~> 6.0'
    pod 'RxBlocking', '~> 6.0'
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
  end
end
```

---

## 📋 代码检查清单

### 提交前检查

- [ ] 代码编译无警告
- [ ] 遵循命名规范
- [ ] 使用正确的访问控制
- [ ] 内存管理正确（无循环引用）
- [ ] UI 更新在主线程
- [ ] 错误处理完整
- [ ] 单元测试覆盖核心逻辑
- [ ] 代码注释清晰
- [ ] 遵循 MVVM 架构
- [ ] 性能优化考虑

### 代码审查要点

- [ ] 架构设计合理
- [ ] 职责分离清晰
- [ ] 可测试性良好
- [ ] 可维护性高
- [ ] 安全性考虑充分
- [ ] 性能影响最小
- [ ] 兼容性良好

---

本规范将随着项目发展和 Swift 语言更新持续完善。如有疑问或建议，请在团队内部讨论。
