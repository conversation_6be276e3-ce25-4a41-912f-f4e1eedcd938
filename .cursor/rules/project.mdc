---
description: Base Guidelines Cursor Agent
globs: *,**/*
alwaysApply: true
---
# iOS/Mac 开发 Cursor AI 辅助模板

基于 Cursor AI 的 iOS/Mac 开发辅助模板，帮助您高效地利用 AI 进行 Objective-C、Swift、UIKit 开发。

## 简介

这个模板提供了一个结构化的项目框架，专为 Cursor IDE 中的 AI 辅助功能设计。它使 AI 能够更好地理解项目架构、技术规范和开发进度，从而提供更精准的代码建议和实现。

基于"AI 不是魔法，而是一个需要明确指导的合作者"的理念，这个模板帮助您：

- 提供清晰的系统架构
- 结构化任务管理
- 定义明确的开发规则
- 跟踪项目进度

## 目录结构

```
project-root/
├── .cursor/rules/
│   ├── core.mdc                      # 核心 AI 行为配置
│   ├── project.mdc                   # 项目规则配置
├── docs/
│   ├── architecture.mermaid          # 系统架构图
│   ├── technical.md                  # 技术文档
│   └── status.md                     # 进度跟踪
├── tasks/
│   └── tasks.md                      # 开发任务分解
└── ChatsGo/                          # 源代码
```

## 各部分说明

### 1. AI 行为配置 (.cursor/rules/)

定义 AI 助手的行为规则和工作流程。包含多个规则文件：

- **core.mdc**：核心行为配置，包括系统上下文、文件变更规则等
- **project.mdc**：项目规则配置

这些规则文件共同定义了：

- 系统上下文和项目架构
- 文件变更规则
- 代码风格和模式
- 架构理解方式
- 任务管理流程
- 错误预防策略

### 2. 系统架构图 (docs/architecture.mermaid)

使用 Mermaid 语法的架构图，展示系统组件和它们之间的关系。AI 会解析此图以理解：

- 模块边界和关系
- 数据流模式
- 组件依赖
- 关注点分离

### 3. 技术文档 (docs/technical.md)

详细的技术规范和实现指南，包括：

- 技术栈概述
- 核心模块说明
- UI 架构
- 网络层设计
- 测试策略
- 依赖管理
- 安全考量
- 部署流程

### 4. 进度跟踪 (docs/status.md)

项目进度的实时快照，用于：

- 记录已完成功能
- 跟踪进行中的任务
- 列出待办项目
- 记录已知问题

### 5. 任务分解 (tasks/tasks.md)

详细的任务定义，包括：

- 唯一任务 ID 和标题
- 状态和优先级
- 详细需求
- 验收标准
- 技术注意事项

## 使用方法

### 初始设置

1. 将此模板克隆至您的项目目录
2. 根据项目需求修改各个模板文件
3. 在 Cursor 中打开项目

### 日常开发流程

1. **任务准备**：在 `tasks/tasks.md` 中定义新任务
2. **AI 上下文设置**：

   ```
   @{docs/architecture.mermaid}
   @{tasks/tasks.md}
   @{docs/status.md}

   我需要实现 [任务ID] 中的功能
   ```

3. **开发实施**：遵循 TDD，先创建测试，再实现功能
4. **更新状态**：在 `docs/status.md` 中更新进度
5. **处理上下文限制**：当 AI 失去上下文时，使用 `@{docs/status.md}` 恢复

### 最佳实践

- **使用 TDD**：先编写测试，再让 AI 实现功能，可以减少 AI 幻觉
- **分解任务**：将复杂任务分解为小块，便于 AI 理解和实现
- **保持文档同步**：随着项目进展，及时更新文档
- **明确规范**：在 `technical.md` 中定义清晰的技术规范
- **重用上下文**：使用文件引用恢复对话上下文

## 常见问题

### 1. AI 生成的代码有 bug 怎么办？

使用 TDD 方法，先编写测试，然后让 AI 实现通过测试的代码。

### 2. 如何处理复杂的业务逻辑？

将复杂逻辑分解为小块，在 `tasks.md` 中详细描述，然后逐步实现。

### 3. 如何处理上下文限制？

使用 `@{docs/status.md}` 和 `@{docs/technical.md}` 等文件引用恢复上下文。

### 4. 如何修改架构图？

编辑 `docs/architecture.mermaid` 文件，遵循 Mermaid 语法。然后在与 AI 对话时引用更新后的文件。

## 使用规范和示例

### 1. 架构图设计规范 (architecture.mermaid)

#### 基本结构

```mermaid
graph TD
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[数据存储层]
    
    subgraph "UI层"
        A1[UIViewController]
        A2[View Models]
        A3[Custom Views]
    end
    
    subgraph "业务层"
        B1[Service Layer]
        B2[Use Cases]
        B3[Domain Models]
    end
    
    subgraph "数据层"
        C1[Repository Pattern]
        C2[Network Layer]
        C3[Cache Layer]
    end
    
    subgraph "存储层"
        D1[Core Data]
        D2[UserDefaults]
        D3[Keychain]
    end
```

#### 模块关系图示例

```mermaid
graph LR
    subgraph "认证模块"
        Auth[AuthenticationManager]
        AuthUI[LoginViewController]
        AuthModel[User]
    end
    
    subgraph "用户模块"
        Profile[ProfileManager]
        ProfileUI[ProfileViewController]
        ProfileModel[UserProfile]
    end
    
    subgraph "网络模块"
        Network[NetworkService]
        API[APIClient]
        Cache[CacheManager]
    end
    
    AuthUI --> Auth
    Auth --> Network
    ProfileUI --> Profile
    Profile --> Network
    Profile --> Auth
```

### 2. 任务管理规范 (tasks.md)

#### 任务ID命名规范

- **功能模块前缀**：AUTH (认证)、UI (界面)、NET (网络)、DATA (数据)
- **编号**：三位数字，如 001、002
- **示例**：AUTH-001、UI-002、NET-003

#### 完整任务示例

```markdown
## AUTH-001：用户登录功能

状态: 进行中
优先级: 高
依赖: NET-001 (网络层基础)

### 需求

- 实现邮箱/密码登录功能
- 支持记住密码选项
- 集成第三方登录 (Apple ID)
- 实现登录状态持久化

### 验收标准

1. 用户可以使用有效邮箱和密码成功登录
2. 登录失败时显示具体错误信息
3. 记住密码功能正常工作
4. Apple ID 登录集成完成
5. 登录状态在应用重启后保持
6. 所有登录流程都有加载状态指示

### 技术注意事项

- 使用 UIKit + Swift 进行开发
- 遵循 MVVM 架构模式
- 密码需要安全存储在 Keychain
- 网络请求需要适当的错误处理
- UI 需要支持深色模式
- 使用 SnapKit 进行 Auto Layout
```

### 3. 技术文档规范 (technical.md)

#### UIKit + MVVM 架构示例

```swift
// MARK: - ViewController
class LoginViewController: UIViewController {
    
    // MARK: - Properties
    private var viewModel: LoginViewModel!
    
    private lazy var emailTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "邮箱"
        textField.borderStyle = .roundedRect
        textField.keyboardType = .emailAddress
        textField.autocapitalizationType = .none
        return textField
    }()
    
    private lazy var passwordTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "密码"
        textField.borderStyle = .roundedRect
        textField.isSecureTextEntry = true
        return textField
    }()
    
    private lazy var loginButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("登录", for: .normal)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupViewModel()
        bindViewModel()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "登录"
        
        // 添加子视图
        view.addSubview(emailTextField)
        view.addSubview(passwordTextField)
        view.addSubview(loginButton)
        view.addSubview(activityIndicator)
        
        // 设置约束 (使用 SnapKit)
        emailTextField.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60)
            make.width.equalTo(280)
            make.height.equalTo(44)
        }
        
        passwordTextField.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(emailTextField.snp.bottom).offset(16)
            make.width.height.equalTo(emailTextField)
        }
        
        loginButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(passwordTextField.snp.bottom).offset(24)
            make.width.height.equalTo(emailTextField)
        }
        
        activityIndicator.snp.makeConstraints { make in
            make.center.equalTo(loginButton)
        }
    }
    
    private func setupViewModel() {
        viewModel = LoginViewModel()
    }
    
    private func bindViewModel() {
        // 监听加载状态
        viewModel.isLoadingDidChange = { [weak self] isLoading in
            DispatchQueue.main.async {
                if isLoading {
                    self?.activityIndicator.startAnimating()
                    self?.loginButton.setTitle("", for: .normal)
                    self?.loginButton.isEnabled = false
                } else {
                    self?.activityIndicator.stopAnimating()
                    self?.loginButton.setTitle("登录", for: .normal)
                    self?.loginButton.isEnabled = true
                }
            }
        }
        
        // 监听登录成功
        viewModel.loginSuccessHandler = { [weak self] in
            DispatchQueue.main.async {
                self?.navigationController?.popViewController(animated: true)
            }
        }
        
        // 监听错误
        viewModel.errorHandler = { [weak self] errorMessage in
            DispatchQueue.main.async {
                self?.showAlert(message: errorMessage)
            }
        }
    }
    
    // MARK: - Actions
    @objc private func loginButtonTapped() {
        viewModel.email = emailTextField.text ?? ""
        viewModel.password = passwordTextField.text ?? ""
        viewModel.login()
    }
    
    // MARK: - Helper Methods
    private func showAlert(message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - ViewModel
class LoginViewModel {
    
    // MARK: - Properties
    var email: String = ""
    var password: String = ""
    
    private var isLoading: Bool = false {
        didSet {
            isLoadingDidChange?(isLoading)
        }
    }
    
    // MARK: - Callbacks
    var isLoadingDidChange: ((Bool) -> Void)?
    var loginSuccessHandler: (() -> Void)?
    var errorHandler: ((String) -> Void)?
    
    // MARK: - Dependencies
    private let authService: AuthenticationServiceProtocol
    
    init(authService: AuthenticationServiceProtocol = AuthenticationService()) {
        self.authService = authService
    }
    
    // MARK: - Methods
    func login() {
        guard !email.isEmpty && !password.isEmpty else {
            errorHandler?("请填写邮箱和密码")
            return
        }
        
        guard isValidEmail(email) else {
            errorHandler?("请输入有效的邮箱地址")
            return
        }
        
        isLoading = true
        
        authService.login(email: email, password: password) { [weak self] result in
            self?.isLoading = false
            
            switch result {
            case .success(let user):
                // 保存用户信息
                UserManager.shared.currentUser = user
                self?.loginSuccessHandler?()
            case .failure(let error):
                self?.errorHandler?(error.localizedDescription)
            }
        }
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let predicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return predicate.evaluate(with: email)
    }
}

// MARK: - Service Protocol
protocol AuthenticationServiceProtocol {
    func login(email: String, password: String, completion: @escaping (Result<User, Error>) -> Void)
}
```

#### 网络层设计示例

```swift
// MARK: - Network Manager
class NetworkManager {
    static let shared = NetworkManager()
    
    private let session: URLSession
    private let baseURL = "https://api.followme.com"
    
    private init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        self.session = URLSession(configuration: configuration)
    }
    
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        responseType: T.Type,
        completion: @escaping (Result<T, NetworkError>) -> Void
    ) {
        guard let url = endpoint.url(baseURL: baseURL) else {
            completion(.failure(.invalidURL))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.httpMethod
        request.allHTTPHeaderFields = endpoint.headers
        request.httpBody = endpoint.body
        
        session.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(.networkError(error)))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(.invalidResponse))
                return
            }
            
            guard 200...299 ~= httpResponse.statusCode else {
                completion(.failure(.serverError(httpResponse.statusCode)))
                return
            }
            
            guard let data = data else {
                completion(.failure(.noData))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                let result = try decoder.decode(T.self, from: data)
                completion(.success(result))
            } catch {
                completion(.failure(.decodingError(error)))
            }
        }.resume()
    }
}

// MARK: - API Endpoint
enum APIEndpoint {
    case login(email: String, password: String)
    case profile
    case logout
    
    var httpMethod: String {
        switch self {
        case .login:
            return "POST"
        case .profile:
            return "GET"
        case .logout:
            return "POST"
        }
    }
    
    var path: String {
        switch self {
        case .login:
            return "/auth/login"
        case .profile:
            return "/user/profile"
        case .logout:
            return "/auth/logout"
        }
    }
    
    var headers: [String: String] {
        var headers = ["Content-Type": "application/json"]
        
        // 添加认证头
        if let token = TokenManager.shared.accessToken {
            headers["Authorization"] = "Bearer \(token)"
        }
        
        return headers
    }
    
    var body: Data? {
        switch self {
        case .login(let email, let password):
            let parameters = ["email": email, "password": password]
            return try? JSONSerialization.data(withJSONObject: parameters)
        case .profile, .logout:
            return nil
        }
    }
    
    func url(baseURL: String) -> URL? {
        return URL(string: baseURL + path)
    }
}

// MARK: - Network Error
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case networkError(Error)
    case invalidResponse
    case serverError(Int)
    case noData
    case decodingError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应"
        case .serverError(let code):
            return "服务器错误: \(code)"
        case .noData:
            return "没有数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        }
    }
}
```

### 4. 状态跟踪规范 (status.md)

#### 标准格式示例

```markdown
# 项目状态跟踪

## 已完成功能

- ✅ 项目基础架构搭建 (ARCH-001)
- ✅ 网络层基础实现 (NET-001)
- ✅ 用户模型定义 (DATA-001)

## 进行中

### 用户认证模块 (AUTH-001)
- ✅ UI 界面设计
- ✅ ViewModel 实现
- 🏗️ 网络请求集成 (80% 完成)
- ⏳ Apple ID 登录集成
- ⏳ 单元测试编写

### 个人资料页面 (UI-002)
- ✅ 基础 UI 布局
- 🏗️ 数据绑定 (50% 完成)
- ⏳ 头像上传功能

## 待办

- 📋 推送通知集成 (PUSH-001)
- 📋 数据缓存优化 (CACHE-001)
- 📋 深色模式适配 (UI-003)

## 已知问题

- ⚠️ 网络请求在慢速连接下可能超时 (NET-001 相关)
- ⚠️ 登录页面在 iPad 上布局需要优化 (UI-001 相关)

## 技术债务

- 🔧 需要重构网络错误处理逻辑
- 🔧 单元测试覆盖率需要提升到 80%

## 性能指标

- 应用启动时间: 1.2s (目标: <1s)
- 内存使用: 平均 45MB (目标: <50MB)
- 崩溃率: 0.1% (目标: <0.5%)

## 备注

- 上次更新时间: 2024-01-15
- 下次里程碑: 2024-01-30 (Beta 版本发布)
- 团队成员: iOS 开发者 x2, UI 设计师 x1
```

### 5. AI 交互最佳实践

#### 上下文设置模板

```
# 开始新功能开发
@{docs/architecture.mermaid}
@{tasks/tasks.md}
@{docs/technical.md}
@{docs/status.md}

我需要实现 AUTH-001 中的用户登录功能。请先分析架构图，然后按照技术规范实现。
```

#### 代码审查模板

```
# 代码审查请求
@{docs/technical.md}
@{src/Authentication/LoginViewController.swift}

请审查这个 LoginViewController 的实现，确保符合项目的技术规范和架构要求。
```

#### 测试驱动开发模板

```
# TDD 实施
@{tasks/tasks.md}
@{docs/technical.md}

针对 AUTH-001 任务，请先编写单元测试，然后实现通过测试的代码。
```

### 6. 文件维护周期

#### 日常维护

- **tasks.md**: 每日更新任务状态
- **status.md**: 每日或每次提交后更新
- **technical.md**: 架构变更时更新
- **architecture.mermaid**: 重大架构调整时更新

#### 定期审查

- **周审查**: 清理已完成任务，规划新任务
- **月审查**: 更新技术文档，评估架构演进
- **季度审查**: 重构文档结构，优化工作流程

## 参考资料

- [Mermaid 语法](mdc:https:/mermaid-js.github.io/mermaid)
- [Cursor Rules](mdc:https:/docs.cursor.com/context/rules)
- [The Ultimate Guide to AI-Powered Development with Cursor: From Chaos to Clean Code](mdc:https:/medium.com/@vrknetha/the-ultimate-guide-to-ai-powered-development-with-cursor-from-chaos-to-clean-code-fc679973bbc4)