# Podfile for ChatsGo iOS Project

platform :ios, '14.0'
use_frameworks! :linkage => :static

# 禁用输入验证以避免警告
install! 'cocoapods', :disable_input_output_paths => true

target 'ChatsGo' do
  pod 'LookinServer', :configurations => ['Debug']
  
  # UI 框架
  pod 'SnapKit', '~> 5.0'
  
  # 网络框架 (未来需要)
  # pod 'Alamofire', '~> 5.0'
  
  # 图片加载 (未来需要)
  # pod 'Kingfisher', '~> 7.0'
  
  # JSON 解析增强 (未来需要)
  # pod 'SwiftyJSON', '~> 5.0'

  target 'ChatsGoTests' do
    inherit! :search_paths
    # 测试框架 (未来需要)
    # pod 'Quick', '~> 6.0'
    # pod 'Nimble', '~> 11.0'
  end

  target 'ChatsGoUITests' do
    inherit! :search_paths
    # UI 测试依赖 (如果需要)
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
      config.build_settings['SWIFT_VERSION'] = '5.0'
      
      # 修复一些常见的编译警告
      config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'
    end
  end
end 
