//
//  SystemInfoManagerTests.swift
//  ChatsGoTests
//
//  Created by AI Assistant on 2024/12/19.
//

import XCTest
@testable import ChatsGo

final class SystemInfoManagerTests: XCTestCase {
    
    var systemInfoManager: SystemInfoManager!
    
    override func setUp() {
        super.setUp()
        systemInfoManager = SystemInfoManager.shared
    }
    
    override func tearDown() {
        systemInfoManager = nil
        super.tearDown()
    }
    
    // MARK: - 电池信息测试
    
    func testGetBatteryInfo() {
        // Given & When
        let batteryInfo = systemInfoManager.getBatteryInfo()
        
        // Then
        XCTAssertGreaterThanOrEqual(batteryInfo.level, 0.0)
        XCTAssertLessThanOrEqual(batteryInfo.level, 1.0)
        XCTAssertFalse(batteryInfo.levelPercentage.isEmpty)
        XCTAssertFalse(batteryInfo.stateDescription.isEmpty)
    }
    
    // MARK: - 存储空间测试
    
    func testGetStorageInfo() {
        // Given & When
        let storageInfo = systemInfoManager.getStorageInfo()
        
        // Then
        XCTAssertGreaterThan(storageInfo.totalSpace, 0)
        XCTAssertGreaterThanOrEqual(storageInfo.availableSpace, 0)
        XCTAssertGreaterThanOrEqual(storageInfo.usedSpace, 0)
        XCTAssertLessThanOrEqual(storageInfo.usedSpace, storageInfo.totalSpace)
        XCTAssertFalse(storageInfo.totalSpaceFormatted.isEmpty)
        XCTAssertFalse(storageInfo.availableSpaceFormatted.isEmpty)
        XCTAssertFalse(storageInfo.usedSpaceFormatted.isEmpty)
        XCTAssertGreaterThanOrEqual(storageInfo.usagePercentage, 0.0)
        XCTAssertLessThanOrEqual(storageInfo.usagePercentage, 1.0)
    }
    
    // MARK: - 内存信息测试
    
    func testGetMemoryInfo() {
        // Given & When
        let memoryInfo = systemInfoManager.getMemoryInfo()
        
        // Then
        XCTAssertGreaterThan(memoryInfo.totalPhysicalMemory, 0)
        XCTAssertGreaterThanOrEqual(memoryInfo.appMemoryUsage, 0)
        XCTAssertGreaterThanOrEqual(memoryInfo.availableMemory, 0)
        XCTAssertFalse(memoryInfo.totalPhysicalMemoryFormatted.isEmpty)
        XCTAssertFalse(memoryInfo.appMemoryUsageFormatted.isEmpty)
        XCTAssertFalse(memoryInfo.availableMemoryFormatted.isEmpty)
    }
    
    // MARK: - 设备信息测试
    
    func testGetDeviceInfo() {
        // Given & When
        let deviceInfo = systemInfoManager.getDeviceInfo()
        
        // Then
        XCTAssertFalse(deviceInfo.name.isEmpty)
        XCTAssertFalse(deviceInfo.model.isEmpty)
        XCTAssertFalse(deviceInfo.localizedModel.isEmpty)
        XCTAssertFalse(deviceInfo.systemName.isEmpty)
        XCTAssertFalse(deviceInfo.systemVersion.isEmpty)
        XCTAssertFalse(deviceInfo.deviceTypeDescription.isEmpty)
        XCTAssertFalse(deviceInfo.orientationDescription.isEmpty)
    }
    
    // MARK: - 网络信息测试
    
    func testGetNetworkInfo() {
        // Given & When
        let networkInfo = systemInfoManager.getNetworkInfo()
        
        // Then
        XCTAssertFalse(networkInfo.statusDescription.isEmpty)
        // 注意：网络状态可能为 true 或 false，都是有效的
    }
    
    // MARK: - 系统信息测试
    
    func testGetSystemInfo() {
        // Given & When
        let systemInfo = systemInfoManager.getSystemInfo()
        
        // Then
        XCTAssertFalse(systemInfo.iOSVersion.isEmpty)
        XCTAssertFalse(systemInfo.appVersion.isEmpty)
        XCTAssertFalse(systemInfo.appBuild.isEmpty)
        XCTAssertFalse(systemInfo.bundleIdentifier.isEmpty)
        XCTAssertFalse(systemInfo.appName.isEmpty)
        XCTAssertGreaterThan(systemInfo.systemUptime, 0)
        XCTAssertFalse(systemInfo.preferredLanguage.isEmpty)
        XCTAssertFalse(systemInfo.appFullVersion.isEmpty)
        XCTAssertFalse(systemInfo.systemUptimeFormatted.isEmpty)
    }
    
    // MARK: - 便捷方法测试
    
    func testGetAllSystemInfo() {
        // Given & When
        let allInfo = systemInfoManager.getAllSystemInfo()
        
        // Then
        XCTAssertTrue(allInfo.keys.contains("battery"))
        XCTAssertTrue(allInfo.keys.contains("storage"))
        XCTAssertTrue(allInfo.keys.contains("memory"))
        XCTAssertTrue(allInfo.keys.contains("device"))
        XCTAssertTrue(allInfo.keys.contains("network"))
        XCTAssertTrue(allInfo.keys.contains("system"))
        
        // 验证字典结构
        XCTAssertNotNil(allInfo["battery"] as? [String: Any])
        XCTAssertNotNil(allInfo["storage"] as? [String: Any])
        XCTAssertNotNil(allInfo["memory"] as? [String: Any])
        XCTAssertNotNil(allInfo["device"] as? [String: Any])
        XCTAssertNotNil(allInfo["network"] as? [String: Any])
        XCTAssertNotNil(allInfo["system"] as? [String: Any])
    }
    
    func testGenerateSystemReport() {
        // Given & When
        let report = systemInfoManager.generateSystemReport()
        
        // Then
        XCTAssertFalse(report.isEmpty)
        XCTAssertTrue(report.contains("=== 系统信息报告 ==="))
        XCTAssertTrue(report.contains("📱 设备信息:"))
        XCTAssertTrue(report.contains("🔋 电池信息:"))
        XCTAssertTrue(report.contains("💾 存储空间:"))
        XCTAssertTrue(report.contains("🧠 内存信息:"))
        XCTAssertTrue(report.contains("🌐 网络状态:"))
        XCTAssertTrue(report.contains("📋 应用信息:"))
    }
    
    // MARK: - 性能测试
    
    func testPerformanceGetBatteryInfo() {
        measure {
            _ = systemInfoManager.getBatteryInfo()
        }
    }
    
    func testPerformanceGetStorageInfo() {
        measure {
            _ = systemInfoManager.getStorageInfo()
        }
    }
    
    func testPerformanceGetMemoryInfo() {
        measure {
            _ = systemInfoManager.getMemoryInfo()
        }
    }
    
    func testPerformanceGetDeviceInfo() {
        measure {
            _ = systemInfoManager.getDeviceInfo()
        }
    }
    
    func testPerformanceGetSystemInfo() {
        measure {
            _ = systemInfoManager.getSystemInfo()
        }
    }
    
    func testPerformanceGenerateSystemReport() {
        measure {
            _ = systemInfoManager.generateSystemReport()
        }
    }
}

// MARK: - Mock 测试类

final class MockSystemInfoManagerTests: XCTestCase {
    
    func testBatteryInfoModel() {
        // Given
        let batteryInfo = BatteryInfo(
            level: 0.75,
            state: .charging,
            isLowPowerModeEnabled: false
        )
        
        // Then
        XCTAssertEqual(batteryInfo.levelPercentage, "75%")
        XCTAssertEqual(batteryInfo.stateDescription, "充电中")
        XCTAssertFalse(batteryInfo.isLowPowerModeEnabled)
    }
    
    func testStorageInfoModel() {
        // Given
        let storageInfo = StorageInfo(
            totalSpace: 128_000_000_000, // 128GB
            availableSpace: 50_000_000_000, // 50GB
            usedSpace: 78_000_000_000 // 78GB
        )
        
        // Then
        XCTAssertEqual(storageInfo.usagePercentage, 0.609375, accuracy: 0.001)
        XCTAssertEqual(storageInfo.usagePercentageString, "60.9%")
        XCTAssertFalse(storageInfo.totalSpaceFormatted.isEmpty)
        XCTAssertFalse(storageInfo.availableSpaceFormatted.isEmpty)
        XCTAssertFalse(storageInfo.usedSpaceFormatted.isEmpty)
    }
    
    func testMemoryInfoModel() {
        // Given
        let memoryInfo = MemoryInfo(
            totalPhysicalMemory: 6_000_000_000, // 6GB
            appMemoryUsage: 150_000_000, // 150MB
            availableMemory: 2_000_000_000 // 2GB
        )
        
        // Then
        XCTAssertFalse(memoryInfo.totalPhysicalMemoryFormatted.isEmpty)
        XCTAssertFalse(memoryInfo.appMemoryUsageFormatted.isEmpty)
        XCTAssertFalse(memoryInfo.availableMemoryFormatted.isEmpty)
    }
} 