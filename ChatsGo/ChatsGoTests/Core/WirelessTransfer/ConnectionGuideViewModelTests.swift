//
//  ConnectionGuideViewModelTests.swift
//  ChatsGoTests
//
//  Created by AI Assistant on 07/07/2025.
//

import XCTest
import Combine
@testable import ChatsGo

/// 连接引导ViewModel的单元测试
/// 遵循测试驱动开发原则，确保业务逻辑的正确性
class ConnectionGuideViewModelTests: XCTestCase {
    
    // MARK: - Properties
    
    var viewModel: ConnectionGuideViewModel!
    var mockStepProvider: MockConnectionGuideStepProvider!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        mockStepProvider = MockConnectionGuideStepProvider()
        viewModel = ConnectionGuideViewModel(stepProvider: mockStepProvider)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        viewModel = nil
        mockStepProvider = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Test Cases
    
    func testInitialState() {
        // Given & When
        // ViewModel已在setUp中初始化
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, 0)
        XCTAssertFalse(viewModel.canProceed)
        XCTAssertFalse(viewModel.shouldShowQRCodeArea)
        XCTAssertTrue(viewModel.steps.isEmpty)
        XCTAssertNil(viewModel.errorMessage)
    }
    
    func testLoadSteps() {
        // Given
        let expectedSteps = ConnectionGuideStep.createDefaultSteps()
        mockStepProvider.mockSteps = expectedSteps
        
        // When
        viewModel.loadSteps()
        
        // Then
        XCTAssertEqual(viewModel.steps.count, expectedSteps.count)
        XCTAssertEqual(viewModel.steps[0].stepNumber, 1)
        XCTAssertTrue(viewModel.steps[0].isCurrent)
        XCTAssertFalse(viewModel.steps[0].isCompleted)
        XCTAssertTrue(viewModel.canProceed)
    }
    
    func testProceedToNextStep() {
        // Given
        viewModel.loadSteps()
        let initialStepIndex = viewModel.currentStepIndex
        
        // When
        viewModel.proceedToNextStep()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, initialStepIndex + 1)
        XCTAssertTrue(viewModel.steps[initialStepIndex].isCompleted)
        XCTAssertFalse(viewModel.steps[initialStepIndex].isCurrent)
        XCTAssertTrue(viewModel.steps[initialStepIndex + 1].isCurrent)
    }
    
    func testProceedToNextStepAtLastStep() {
        // Given
        viewModel.loadSteps()
        // 移动到最后一步
        while viewModel.canProceed {
            viewModel.proceedToNextStep()
        }
        let lastStepIndex = viewModel.currentStepIndex
        
        // When
        viewModel.proceedToNextStep()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, lastStepIndex) // 应该保持不变
        XCTAssertFalse(viewModel.canProceed)
    }
    
    func testGoToPreviousStep() {
        // Given
        viewModel.loadSteps()
        viewModel.proceedToNextStep() // 移动到第二步
        let currentStepIndex = viewModel.currentStepIndex
        
        // When
        viewModel.goToPreviousStep()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, currentStepIndex - 1)
        XCTAssertFalse(viewModel.steps[currentStepIndex - 1].isCompleted)
        XCTAssertTrue(viewModel.steps[currentStepIndex - 1].isCurrent)
        XCTAssertFalse(viewModel.steps[currentStepIndex].isCurrent)
    }
    
    func testGoToPreviousStepAtFirstStep() {
        // Given
        viewModel.loadSteps()
        let initialStepIndex = viewModel.currentStepIndex
        
        // When
        viewModel.goToPreviousStep()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, initialStepIndex) // 应该保持不变
    }
    
    func testQRCodeAreaVisibility() {
        // Given
        viewModel.loadSteps()
        
        // When & Then - 第1步不显示QR码区域
        XCTAssertFalse(viewModel.shouldShowQRCodeArea)
        
        // When & Then - 第2步不显示QR码区域
        viewModel.proceedToNextStep()
        XCTAssertFalse(viewModel.shouldShowQRCodeArea)
        
        // When & Then - 第3步显示QR码区域
        viewModel.proceedToNextStep()
        XCTAssertTrue(viewModel.shouldShowQRCodeArea)
        
        // When & Then - 第4步不显示QR码区域
        viewModel.proceedToNextStep()
        XCTAssertFalse(viewModel.shouldShowQRCodeArea)
    }
    
    func testResetGuide() {
        // Given
        viewModel.loadSteps()
        viewModel.proceedToNextStep()
        viewModel.proceedToNextStep()
        
        // When
        viewModel.resetGuide()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, 0)
        XCTAssertTrue(viewModel.steps[0].isCurrent)
        XCTAssertFalse(viewModel.steps[0].isCompleted)
        XCTAssertFalse(viewModel.shouldShowQRCodeArea)
    }
    
    func testCompleteGuide() {
        // Given
        viewModel.loadSteps()
        // 移动到最后一步
        while viewModel.canProceed {
            viewModel.proceedToNextStep()
        }
        let lastStepIndex = viewModel.currentStepIndex
        
        // When
        viewModel.completeGuide()
        
        // Then
        XCTAssertTrue(viewModel.steps[lastStepIndex].isCompleted)
        XCTAssertFalse(viewModel.steps[lastStepIndex].isCurrent)
    }
    
    func testConnectToHotspot() {
        // Given
        viewModel.loadSteps()
        
        // When
        viewModel.connectToHotspot()
        
        // Then
        // 这里主要测试方法不会崩溃
        // 实际的连接逻辑可能需要mock网络服务
        XCTAssertNoThrow(viewModel.connectToHotspot())
    }
}

// MARK: - Mock Classes

/// 模拟的连接引导步骤提供者
/// 用于单元测试，遵循依赖倒置原则
class MockConnectionGuideStepProvider: ConnectionGuideStepProviding {
    
    var mockSteps: [ConnectionGuideStep] = []
    
    func provideSteps() -> [ConnectionGuideStep] {
        return mockSteps
    }
}

// MARK: - Test Extensions

extension ConnectionGuideViewModelTests {
    
    /// 测试ViewModel的响应式属性
    func testReactiveProperties() {
        // Given
        let expectation = XCTestExpectation(description: "Steps should be published")
        
        // When
        viewModel.$steps
            .dropFirst() // 跳过初始值
            .sink { steps in
                XCTAssertFalse(steps.isEmpty)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.loadSteps()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
    }
    
    /// 测试当前步骤索引的变化
    func testCurrentStepIndexChanges() {
        // Given
        viewModel.loadSteps()
        let expectation = XCTestExpectation(description: "Current step index should change")
        
        // When
        viewModel.$currentStepIndex
            .dropFirst() // 跳过初始值
            .sink { index in
                XCTAssertEqual(index, 1)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        viewModel.proceedToNextStep()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
    }
}
