//
//  ConnectionGuideStepView.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import UIKit
import SnapKit

/// 连接引导步骤视图组件
/// 遵循单一职责原则，只负责显示单个引导步骤
class ConnectionGuideStepView: UIView {
    
    // MARK: - UI Components
    
    /// 步骤指示器（绿色圆点）
    private lazy var stepIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGreen
        view.layer.cornerRadius = 8
        view.layer.masksToBounds = true
        return view
    }()
    
    /// 连接线（连接到下一个步骤）
    private lazy var connectionLine: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGreen
        return view
    }()
    
    /// 步骤标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        label.numberOfLines = 1
        return label
    }()
    
    /// 步骤描述标签
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()
    
    /// 内容容器视图
    private lazy var contentContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // MARK: - Properties
    
    /// 当前步骤数据
    private var step: ConnectionGuideStep?
    
    /// 是否为最后一个步骤
    private var isLastStep: Bool = false
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Public Methods
    
    /// 配置步骤视图
    /// - Parameters:
    ///   - step: 步骤数据
    ///   - isLastStep: 是否为最后一个步骤
    func configure(with step: ConnectionGuideStep, isLastStep: Bool = false) {
        self.step = step
        self.isLastStep = isLastStep
        
        updateUI()
    }
    
    // MARK: - Private Methods
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(stepIndicator)
        addSubview(connectionLine)
        addSubview(contentContainer)
        
        contentContainer.addSubview(titleLabel)
        contentContainer.addSubview(descriptionLabel)
    }
    
    private func setupConstraints() {
        // 步骤指示器约束
        stepIndicator.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.top.equalToSuperview().offset(4)
            make.size.equalTo(16)
        }
        
        // 连接线约束
        connectionLine.snp.makeConstraints { make in
            make.centerX.equalTo(stepIndicator)
            make.top.equalTo(stepIndicator.snp.bottom).offset(4)
            make.width.equalTo(2)
            make.bottom.equalToSuperview()
        }
        
        // 内容容器约束
        contentContainer.snp.makeConstraints { make in
            make.leading.equalTo(stepIndicator.snp.trailing).offset(16)
            make.trailing.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20) // 为连接线留出空间
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        // 描述标签约束
        descriptionLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.bottom.equalToSuperview()
        }
    }
    
    private func updateUI() {
        guard let step = step else { return }
        
        // 更新文本内容
        titleLabel.text = step.title
        descriptionLabel.text = step.description
        
        // 更新步骤指示器状态
        updateStepIndicatorAppearance()
        
        // 更新连接线可见性
        connectionLine.isHidden = isLastStep
    }
    
    private func updateStepIndicatorAppearance() {
        guard let step = step else { return }
        
        if step.isCompleted {
            // 已完成状态：绿色背景，白色对勾
            stepIndicator.backgroundColor = .systemGreen
            addCheckmarkToIndicator()
        } else if step.isCurrent {
            // 当前步骤：绿色背景，白色数字
            stepIndicator.backgroundColor = .systemGreen
            addNumberToIndicator(step.stepNumber)
        } else {
            // 未开始状态：灰色背景，灰色数字
            stepIndicator.backgroundColor = .systemGray4
            addNumberToIndicator(step.stepNumber)
        }
    }
    
    private func addCheckmarkToIndicator() {
        // 清除之前的子视图
        stepIndicator.subviews.forEach { $0.removeFromSuperview() }
        
        // 添加对勾图标
        let checkmarkImageView = UIImageView()
        checkmarkImageView.image = UIImage(systemName: "checkmark")
        checkmarkImageView.tintColor = .white
        checkmarkImageView.contentMode = .scaleAspectFit
        
        stepIndicator.addSubview(checkmarkImageView)
        checkmarkImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(10)
        }
    }
    
    private func addNumberToIndicator(_ number: Int) {
        // 清除之前的子视图
        stepIndicator.subviews.forEach { $0.removeFromSuperview() }
        
        // 添加数字标签
        let numberLabel = UILabel()
        numberLabel.text = "\(number)"
        numberLabel.font = .systemFont(ofSize: 10, weight: .bold)
        numberLabel.textColor = step?.isCurrent == true ? .white : .systemGray2
        numberLabel.textAlignment = .center
        
        stepIndicator.addSubview(numberLabel)
        numberLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
}

// MARK: - ConnectionGuideStepView + Animation

extension ConnectionGuideStepView {
    
    /// 播放步骤完成动画
    func animateStepCompletion() {
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: [], animations: {
            self.stepIndicator.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.2) {
                self.stepIndicator.transform = .identity
            }
        }
    }
    
    /// 播放步骤激活动画
    func animateStepActivation() {
        UIView.animate(withDuration: 0.2) {
            self.stepIndicator.alpha = 0.7
        } completion: { _ in
            UIView.animate(withDuration: 0.2) {
                self.stepIndicator.alpha = 1.0
            }
        }
    }
}
