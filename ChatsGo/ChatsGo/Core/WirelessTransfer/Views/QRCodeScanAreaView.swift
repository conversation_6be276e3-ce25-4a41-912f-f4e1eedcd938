//
//  QRCodeScanAreaView.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import UIKit
import SnapKit

/// QR码扫描区域视图组件
/// 遵循单一职责原则，只负责显示QR码扫描区域的UI
class QRCodeScanAreaView: UIView {
    
    // MARK: - UI Components
    
    /// QR码占位符容器
    private lazy var qrCodeContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemGray4.cgColor
        view.layer.masksToBounds = true
        return view
    }()
    
    /// QR码占位符图标
    private lazy var qrCodePlaceholderImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "qrcode")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// QR码占位符文本
    private lazy var placeholderLabel: UILabel = {
        let label = UILabel()
        label.text = "QR Code will appear here"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemGray2
        label.textAlignment = .center
        return label
    }()
    
    /// 备注标题
    private lazy var remarkTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Remark :"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .label
        return label
    }()
    
    /// 备注内容
    private lazy var remarkContentLabel: UILabel = {
        let label = UILabel()
        label.text = "You can also manually connect to the hotspot turned on by the receiving phone."
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()
    
    /// 连接热点按钮
    private lazy var connectToHotspotButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Connect to hotspot >", for: .normal)
        button.setTitleColor(.systemBlue, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.contentHorizontalAlignment = .leading
        return button
    }()
    
    // MARK: - Properties
    
    /// 连接热点按钮点击回调
    var onConnectToHotspotTapped: (() -> Void)?
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    // MARK: - Public Methods
    
    /// 显示实际的QR码图片
    /// - Parameter qrCodeImage: QR码图片
    func showQRCode(_ qrCodeImage: UIImage) {
        qrCodePlaceholderImageView.image = qrCodeImage
        qrCodePlaceholderImageView.tintColor = nil
        placeholderLabel.isHidden = true
        
        // 添加显示动画
        animateQRCodeAppearance()
    }
    
    /// 隐藏QR码，显示占位符
    func hideQRCode() {
        qrCodePlaceholderImageView.image = UIImage(systemName: "qrcode")
        qrCodePlaceholderImageView.tintColor = .systemGray3
        placeholderLabel.isHidden = false
    }
    
    /// 设置加载状态
    /// - Parameter isLoading: 是否正在加载
    func setLoadingState(_ isLoading: Bool) {
        if isLoading {
            showLoadingIndicator()
        } else {
            hideLoadingIndicator()
        }
    }
    
    // MARK: - Private Methods
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(qrCodeContainer)
        addSubview(remarkTitleLabel)
        addSubview(remarkContentLabel)
        addSubview(connectToHotspotButton)
        
        qrCodeContainer.addSubview(qrCodePlaceholderImageView)
        qrCodeContainer.addSubview(placeholderLabel)
    }
    
    private func setupConstraints() {
        // QR码容器约束
        qrCodeContainer.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(200)
        }
        
        // QR码占位符图标约束
        qrCodePlaceholderImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(80)
        }
        
        // 占位符文本约束
        placeholderLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(qrCodePlaceholderImageView.snp.bottom).offset(12)
        }
        
        // 备注标题约束
        remarkTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(qrCodeContainer.snp.bottom).offset(24)
            make.leading.equalToSuperview().offset(20)
        }
        
        // 备注内容约束
        remarkContentLabel.snp.makeConstraints { make in
            make.top.equalTo(remarkTitleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // 连接热点按钮约束
        connectToHotspotButton.snp.makeConstraints { make in
            make.top.equalTo(remarkContentLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(20)
            make.bottom.equalToSuperview()
            make.height.equalTo(44)
        }
    }
    
    private func setupActions() {
        connectToHotspotButton.addTarget(self, action: #selector(connectToHotspotButtonTapped), for: .touchUpInside)
    }
    
    @objc private func connectToHotspotButtonTapped() {
        onConnectToHotspotTapped?()
    }
    
    private func animateQRCodeAppearance() {
        qrCodeContainer.alpha = 0
        qrCodeContainer.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [], animations: {
            self.qrCodeContainer.alpha = 1
            self.qrCodeContainer.transform = .identity
        })
    }
    
    private func showLoadingIndicator() {
        // 移除现有的加载指示器
        qrCodeContainer.subviews.compactMap { $0 as? UIActivityIndicatorView }.forEach { $0.removeFromSuperview() }
        
        let loadingIndicator = UIActivityIndicatorView(style: .medium)
        loadingIndicator.color = .systemGray3
        loadingIndicator.startAnimating()
        
        qrCodeContainer.addSubview(loadingIndicator)
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        // 隐藏其他内容
        qrCodePlaceholderImageView.isHidden = true
        placeholderLabel.isHidden = true
    }
    
    private func hideLoadingIndicator() {
        // 移除加载指示器
        qrCodeContainer.subviews.compactMap { $0 as? UIActivityIndicatorView }.forEach { $0.removeFromSuperview() }
        
        // 显示其他内容
        qrCodePlaceholderImageView.isHidden = false
        placeholderLabel.isHidden = false
    }
}

// MARK: - QRCodeScanAreaView + Accessibility

extension QRCodeScanAreaView {
    
    private func setupAccessibility() {
        qrCodeContainer.isAccessibilityElement = true
        qrCodeContainer.accessibilityLabel = "QR Code scanning area"
        qrCodeContainer.accessibilityHint = "QR code will be displayed here for device connection"
        
        connectToHotspotButton.accessibilityLabel = "Connect to hotspot"
        connectToHotspotButton.accessibilityHint = "Manually connect to the receiving phone's hotspot"
    }
}
