//
//  WirelessTransManagerBridge.h
//  ChatsGo
//
//  Created by Subo on 06/07/2025.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// Swift可见的枚举定义
typedef NS_ENUM(NSInteger, WTMDataType) {
    WTMDataTypeOther = -1,
    WTMDataTypePhoto = 0,
    WTMDataTypeVideo,
    WTMDataTypeAudio,
    WTMDataTypeContact,
    WTMDataTypeCalendar,
    WTMDataTypeFile
};

typedef NS_ENUM(NSInteger, WTMCallbackType) {
    WTMCallbackTypeStart = 0,
    WTM<PERSON><PERSON>backTypeProgress,
    WTMCallbackTypeTransData,
    WTMCallbackTypeEnd
};

typedef NS_ENUM(NSInteger, WTMCallbackCode) {
    WTMCallbackCodeSuccess = 0,
    WTMCallbackCodeFail,
    WTMCallbackCodeStop,
    WTMCallbackCodeReceiveError = 100,
    WTMCallbackCodeSaveError,
    WTMCallbackCodeJsonParseFail,
    WTMCallbackCodeNotEnoughDiskSpace,
    WTMCallbackCodeStatusError
};

// 回调Block定义
typedef void (^WTMCallbackBlock)(WTMCallbackType callbackType, WTMCallbackCode code, NSUInteger size);

// Objective-C包装类
@interface WirelessTransManagerBridge : NSObject

/**
 * 创建WirelessTransManager实例
 */
+ (instancetype)createManager;

/**
 * 连接到Socket服务器
 * @param ip 服务器IP地址
 * @param port 服务器端口
 * @return 连接是否成功
 */
- (BOOL)connectToIP:(NSString *)ip port:(NSInteger)port;

/**
 * 设置回调函数
 * @param callback 回调Block
 */
- (void)setCallback:(WTMCallbackBlock)callback;

/**
 * 开始接收数据
 */
- (void)startReceiving;

/**
 * 停止接收数据
 */
- (void)stopReceiving;

/**
 * 释放资源
 */
- (void)cleanup;

@end

NS_ASSUME_NONNULL_END
