//
//  WirelessTransManagerBridge.m
//  ChatsGo
//
//  Created by <PERSON>o on 06/07/2025.
//

#import "WirelessTransManagerBridge.h"
#import "IWirelessTransManager.h"
#import "WirelessTransPreDefine.h"

@interface WirelessTransManagerBridge ()
@property (nonatomic, assign) IWirelessTransManager* manager;
@property (nonatomic, copy) WTMCallbackBlock callbackBlock;
@end

@implementation WirelessTransManagerBridge {
    dispatch_queue_t _internalQueue;
}

+ (instancetype)createManager {
    return [[self alloc] init];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _manager = CreateWirelessTransManager();
        _internalQueue = dispatch_queue_create("com.yourcompany.wirelesstrans.queue", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (void)dealloc {
    dispatch_sync(_internalQueue, ^{
        [self cleanup];
    });
}

- (BOOL)connectToIP:(NSString *)ip port:(NSInteger)port {
    __block BOOL result = NO;
    dispatch_sync(_internalQueue, ^{
        if (!self.manager) {
            return;
        }
        const char* ipCString = [ip UTF8String];
        result = self.manager->Connect(ipCString, (int)port);
    });
    return result;
}

- (void)setCallback:(WTMCallbackBlock)callback {
    self.callbackBlock = callback;

    if (self.manager) {
        // 设置C++回调函数，它会调用我们的Block
        self.manager->SetCallback(WirelessTransCallbackWrapper, (__bridge void*)self);
    }
}

- (void)startReceiving {
    dispatch_async(_internalQueue, ^{
        if (self.manager) {
            self.manager->ReceiveData();
        }
    });
}

- (void)stopReceiving {
    dispatch_async(_internalQueue, ^{
        if (self.manager) {
            self.manager->Stop();
        }
    });
}

- (void)cleanup {
    if (self.manager) {
        self.manager->Stop();

        // 方案1：如果C++库提供了DestroyWirelessTransManager函数（推荐）
        // DestroyWirelessTransManager(self.manager);

        // 方案2：如果没有销毁函数，检查是否可以安全删除
        // 如果C++库的头文件已经添加了虚析构函数，可以直接delete
        // delete self.manager;

        // 方案3：临时方案 - 抑制警告（不推荐长期使用）
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdelete-non-virtual-dtor"
        delete self.manager;
        #pragma clang diagnostic pop

        self.manager = NULL;
    }
    self.callbackBlock = nil;
}

// C++回调函数的包装
static void WirelessTransCallbackWrapper(ENCallbackType enCallbackType, int code, size_t size, void* data) {
    WirelessTransManagerBridge* bridge = (__bridge WirelessTransManagerBridge*)data;

    if (bridge.callbackBlock) {
        // 转换枚举类型
        WTMCallbackType callbackType = (WTMCallbackType)enCallbackType;
        WTMCallbackCode callbackCode = (WTMCallbackCode)code;

        // 在主线程执行回调
        dispatch_async(dispatch_get_main_queue(), ^{
            bridge.callbackBlock(callbackType, callbackCode, size);
        });
    }
}

@end
