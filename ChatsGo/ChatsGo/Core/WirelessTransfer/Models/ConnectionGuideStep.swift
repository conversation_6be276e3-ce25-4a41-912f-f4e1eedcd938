//
//  ConnectionGuideStep.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import Foundation

/// 连接引导步骤数据模型
/// 遵循单一职责原则，只负责表示引导步骤的数据
struct ConnectionGuideStep {
    
    // MARK: - Properties
    
    /// 步骤编号
    let stepNumber: Int
    
    /// 步骤标题
    let title: String
    
    /// 步骤描述
    let description: String
    
    /// 是否已完成
    var isCompleted: Bool
    
    /// 是否为当前步骤
    var isCurrent: Bool
    
    // MARK: - Initialization
    
    init(stepNumber: Int, title: String, description: String, isCompleted: Bool = false, isCurrent: Bool = false) {
        self.stepNumber = stepNumber
        self.title = title
        self.description = description
        self.isCompleted = isCompleted
        self.isCurrent = isCurrent
    }
}

// MARK: - ConnectionGuideStep + Factory

extension ConnectionGuideStep {
    
    /// 创建默认的连接引导步骤
    /// - Returns: 包含所有步骤的数组
    static func createDefaultSteps() -> [ConnectionGuideStep] {
        return [
            ConnectionGuideStep(
                stepNumber: 1,
                title: "STEP 1",
                description: "On the receiving phone, download and install ChatsGo from Google Play to receive data.",
                isCurrent: true
            ),
            ConnectionGuideStep(
                stepNumber: 2,
                title: "STEP 2",
                description: "Open ChatsGo on the receiving phone and select the function:\n\"Phone to Phone > Android to Android > Receive data\""
            ),
            ConnectionGuideStep(
                stepNumber: 3,
                title: "STEP 3",
                description: "Scan the QR code displayed on the receiving phone to connect the two devices."
            ),
            ConnectionGuideStep(
                stepNumber: 4,
                title: "STEP 4",
                description: "Start transferring data."
            )
        ]
    }
}

// MARK: - ConnectionGuideStep + Equatable

extension ConnectionGuideStep: Equatable {
    static func == (lhs: ConnectionGuideStep, rhs: ConnectionGuideStep) -> Bool {
        return lhs.stepNumber == rhs.stepNumber &&
               lhs.title == rhs.title &&
               lhs.description == rhs.description
    }
}

// MARK: - ConnectionGuideStep + Hashable

extension ConnectionGuideStep: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(stepNumber)
        hasher.combine(title)
        hasher.combine(description)
    }
}
