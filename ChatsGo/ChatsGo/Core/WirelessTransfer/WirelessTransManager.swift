//
//  WirelessTransManager.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 06/07/2025.
//

import Foundation

/// Swift封装的无线传输管理器
public class WirelessTransManager {

    /// 回调事件类型
    public enum CallbackType: Int {
        case start = 0
        case progress
        case transData
        case end
    }

    /// 回调状态码
    public enum CallbackCode: Int {
        case success = 0
        case fail
        case stop
        case receiveError = 100
        case saveError
        case jsonParseFail
        case notEnoughDiskSpace
        case statusError
    }

    /// 数据类型
    public enum DataType: Int {
        case other = -1
        case photo = 0
        case video
        case audio
        case contact
        case calendar
        case file
    }

    /// 回调函数类型定义
    public typealias CallbackHandler = (CallbackType, CallbackCode, UInt) -> Void

    private let bridge: WirelessTransManagerBridge
    private var callbackHandler: CallbackHandler?

    /// 创建WirelessTransManager实例
    public init() {
        self.bridge = WirelessTransManagerBridge.createManager()
    }

    /// 连接到Socket服务器
    /// - Parameters:
    ///   - ip: 服务器IP地址
    ///   - port: 服务器端口
    /// - Returns: 连接是否成功
    public func connect(ip: String, port: Int) -> Bool {
        return bridge.connect(toIP: ip, port: port)
    }

    /// 设置回调函数
    /// - Parameter callback: 回调处理函数
    public func setCallback(_ callback: @escaping CallbackHandler) {
        self.callbackHandler = callback

        bridge.setCallback { [weak self] (type, code, size) in
            guard let self = self,
                  let callbackType = CallbackType(rawValue: type.rawValue),
                  let callbackCode = CallbackCode(rawValue: code.rawValue) else {
                return
            }

            self.callbackHandler?(callbackType, callbackCode, size)
        }
    }

    /// 开始接收数据
    public func startReceiving() {
        bridge.startReceiving()
    }

    /// 停止接收数据
    public func stopReceiving() {
        bridge.stopReceiving()
    }

    /// 释放资源
    public func cleanup() {
        bridge.cleanup()
    }

    deinit {
        cleanup()
    }
}

// MARK: - 便利方法
extension WirelessTransManager {

    /// 连接到服务器的便利方法
    /// - Parameters:
    ///   - host: 主机地址
    ///   - port: 端口号
    ///   - completion: 连接结果回调
    public func connect(to host: String, port: Int, completion: @escaping (Bool) -> Void) {
        DispatchQueue.global(qos: .background).async { [weak self] in
            let result = self?.connect(ip: host, port: port) ?? false
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }

    /// 设置简化的回调处理
    /// - Parameters:
    ///   - onStart: 开始传输时的回调
    ///   - onProgress: 进度更新时的回调
    ///   - onComplete: 传输完成时的回调
    ///   - onError: 发生错误时的回调
    public func setCallbacks(
        onStart: (() -> Void)? = nil,
        onProgress: ((UInt) -> Void)? = nil,
        onComplete: ((UInt) -> Void)? = nil,
        onError: ((CallbackCode) -> Void)? = nil
    ) {
        setCallback { type, code, size in
            switch type {
            case .start:
                onStart?()
            case .progress:
                onProgress?(size)
            case .transData, .end:
                if code == .success {
                    onComplete?(size)
                } else {
                    onError?(code)
                }
            }
        }
    }
}

// MARK: - 错误处理
extension WirelessTransManager.CallbackCode {

    /// 获取错误描述
    public var errorDescription: String {
        switch self {
        case .success:
            return "Success"
        case .fail:
            return "General failure"
        case .stop:
            return "Operation stopped"
        case .receiveError:
            return "Receive error"
        case .saveError:
            return "Save error"
        case .jsonParseFail:
            return "JSON parse failed"
        case .notEnoughDiskSpace:
            return "Not enough disk space"
        case .statusError:
            return "Status error"
        }
    }

    /// 是否为错误状态
    public var isError: Bool {
        return self != .success
    }
}
