//
//  UIColor+Hex.swift
//  STDUIKit
//
//  Created by Subo on 2023/2/16.
//

import UIKit

public extension UIColor {
    convenience init(hex: String) {
        var hex: String = hex.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()

        // 移除 # 前缀
        if hex.hasPrefix("#") {
            hex.removeFirst()
        } else if hex.hasPrefix("0X") {
            // 移除开头的 0X
            hex.removeFirst(2)
        }

        // 将 hex 转换为整数值
        guard let hexValue = Int(hex, radix: 16) else {
            self.init(red: 255, green: 255, blue: 255, alpha: 1)
            return
        }

        let red, green, blue, alpha: CGFloat

        switch hex.count {
        case 3: // RGB 格式，例如 #FFF
            red = CGFloat((hexValue & 0xF00) >> 8) / 15.0
            green = CGFloat((hexValue & 0x0F0) >> 4) / 15.0
            blue = CGFloat(hexValue & 0x00F) / 15.0
            alpha = 1.0

        case 4: // ARGB 格式，例如 #0FFF
            alpha = CGFloat((hexValue & 0xF000) >> 12) / 15.0
            red = CGFloat((hexValue & 0x0F00) >> 8) / 15.0
            green = CGFloat((hexValue & 0x00F0) >> 4) / 15.0
            blue = CGFloat(hexValue & 0x000F) / 15.0
        case 6: // RGB 格式，例如 #FF00FF
            red = CGFloat((hexValue & 0xFF0000) >> 16) / 255.0
            green = CGFloat((hexValue & 0x00FF00) >> 8) / 255.0
            blue = CGFloat(hexValue & 0x0000FF) / 255.0
            alpha = 1.0

        case 8: // ARGB 格式，例如 #FF00FF80
            alpha = CGFloat((hexValue & 0xFF000000) >> 24) / 255.0
            red = CGFloat((hexValue & 0x00FF0000) >> 16) / 255.0
            green = CGFloat((hexValue & 0x0000FF00) >> 8) / 255.0
            blue = CGFloat(hexValue & 0x000000FF) / 255.0

        default:
            self.init(red: 255, green: 255, blue: 255, alpha: 1)
            return
        }

        self.init(red: red, green: green, blue: blue, alpha: alpha)
    }

    /// RGB integer color initializer.
    /// - Parameters:
    ///   - red: Red component as integer.
    ///   - green: Green component as integer.
    ///   - blue: Blue component as integer.
    convenience init(red: Int, green: Int, blue: Int) {
        self.init(
            red: CGFloat(red) / 255.0,
            green: CGFloat(green) / 255.0,
            blue: CGFloat(blue) / 255.0,
            alpha: 1.0
        )
    }

    // MARK: - Properties

    // Color to hex string
    var toHex: String? {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        let multiplier = CGFloat(255.999999)

        guard self.getRed(&red, green: &green, blue: &blue, alpha: &alpha) else {
            return nil
        }

        if alpha == 1.0 {
            return String(
                format: "#%02lX%02lX%02lX",
                Int(red * multiplier),
                Int(green * multiplier),
                Int(blue * multiplier)
            )
        } else {
            return String(
                format: "#%02lX%02lX%02lX%02lX",
                Int(red * multiplier),
                Int(green * multiplier),
                Int(blue * multiplier),
                Int(alpha * multiplier)
            )
        }
    }
}
