//
//  SystemInfoManager.swift
//  ChatsGo
//
//  Created by AI Assistant on 2024/12/19.
//

import Foundation
import UIKit
import Darwin.Mach
import Network

// MARK: - 系统信息管理器协议
protocol SystemInfoManagerProtocol {
    /// 获取电池信息
    func getBatteryInfo() -> BatteryInfo

    /// 获取存储空间信息
    func getStorageInfo() -> StorageInfo

    /// 获取内存使用信息
    func getMemoryInfo() -> MemoryInfo

    /// 获取设备信息
    func getDeviceInfo() -> DeviceInfo

    /// 获取网络信息
    func getNetworkInfo() -> NetworkInfo

    /// 获取系统版本信息
    func getSystemInfo() -> SystemInfo
}

// MARK: - 数据模型

/// 电池信息
struct BatteryInfo {
    let level: Float
    let state: UIDevice.BatteryState
    let isLowPowerModeEnabled: Bool

    var levelPercentage: String { String(format: "%.0f%%", level * 100) }
    var stateDescription: String {
        switch state {
        case .unknown: return "未知"
        case .unplugged: return "未充电"
        case .charging: return "充电中"
        case .full: return "已充满"
        @unknown default: return "未知"
        }
    }
}

/// 存储空间信息
struct StorageInfo {
    let totalSpace: Int64
    let availableSpace: Int64
    let usedSpace: Int64

    var totalSpaceFormatted: String { ByteCountFormatter.string(fromByteCount: totalSpace, countStyle: .file) }
    var availableSpaceFormatted: String { ByteCountFormatter.string(fromByteCount: availableSpace, countStyle: .file) }
    var usedSpaceFormatted: String { ByteCountFormatter.string(fromByteCount: usedSpace, countStyle: .file) }
    var usagePercentage: Float { totalSpace > 0 ? Float(usedSpace) / Float(totalSpace) : 0.0 }
    var usagePercentageString: String { String(format: "%.1f%%", usagePercentage * 100) }
}

/// 内存使用信息
struct MemoryInfo {
    let totalPhysicalMemory: Int64
    let appMemoryUsage: Int64
    let availableMemory: Int64

    var totalPhysicalMemoryFormatted: String { ByteCountFormatter.string(fromByteCount: totalPhysicalMemory, countStyle: .memory) }
    var appMemoryUsageFormatted: String { ByteCountFormatter.string(fromByteCount: appMemoryUsage, countStyle: .memory) }
    var availableMemoryFormatted: String { ByteCountFormatter.string(fromByteCount: availableMemory, countStyle: .memory) }
}

/// 设备信息
struct DeviceInfo {
    let name: String
    let model: String
    let localizedModel: String
    let systemName: String
    let systemVersion: String
    let identifierForVendor: String?
    let orientation: UIDeviceOrientation
    let userInterfaceIdiom: UIUserInterfaceIdiom

    var deviceTypeDescription: String {
        switch userInterfaceIdiom {
        case .phone: return "iPhone"
        case .pad: return "iPad"
        case .tv: return "Apple TV"
        case .carPlay: return "CarPlay"
        case .mac: return "Mac"
        case .vision: return "Apple Vision"
        case .unspecified: return "Unknown Device Type"
        @unknown default: return "Unknown Device Type"
        }
    }

    var orientationDescription: String {
        switch orientation {
        case .unknown: return "未知方向"
        case .portrait: return "竖屏"
        case .portraitUpsideDown: return "倒立竖屏"
        case .landscapeLeft: return "左横屏"
        case .landscapeRight: return "右横屏"
        case .faceUp: return "平放朝上"
        case .faceDown: return "平放朝下"
        @unknown default: return "未知方向"
        }
    }
}

/// 网络信息
struct NetworkInfo {
    let isWiFiAvailable: Bool
    let isCellularAvailable: Bool
    let isNetworkReachable: Bool

    var statusDescription: String {
        if !isNetworkReachable {
            return "无网络连接"
        } else if isWiFiAvailable {
            return "Wi-Fi 连接"
        } else if isCellularAvailable {
            return "蜂窝网络连接"
        } else {
            return "其他网络连接" // 例如：有线网络等
        }
    }
}

/// 系统信息
struct SystemInfo {
    let iOSVersion: String
    let appVersion: String
    let appBuild: String
    let bundleIdentifier: String
    let appName: String
    let systemUptime: TimeInterval
    let timeZone: TimeZone
    let preferredLanguage: String

    var appFullVersion: String { "\(appVersion) (\(appBuild))" }
    var systemUptimeFormatted: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: systemUptime) ?? "未知"
    }
}


// MARK: - 系统信息管理器实现

final class SystemInfoManager: SystemInfoManagerProtocol {

    static let shared = SystemInfoManager()

    // 2. 使用 NWPathMonitor 进行网络监控
    private let pathMonitor: NWPathMonitor
    private let monitorQueue = DispatchQueue(label: "SystemInfoManager.NetworkMonitor")

    // 用于存储最新网络状态的属性
    private(set) var isNetworkReachable: Bool = false
    private(set) var isWiFiAvailable: Bool = false
    private(set) var isCellularAvailable: Bool = false

    private init() {
        // 启用电池监控
        UIDevice.current.isBatteryMonitoringEnabled = true

        // 初始化并启动网络监视器
        pathMonitor = NWPathMonitor()
        pathMonitor.pathUpdateHandler = { [weak self] path in
            self?.isNetworkReachable = path.status == .satisfied
            self?.isWiFiAvailable = path.usesInterfaceType(.wifi)
            self?.isCellularAvailable = path.usesInterfaceType(.cellular)
        }
        pathMonitor.start(queue: monitorQueue)
    }

    // 注意：此处不包含 deinit 方法，因为单例的生命周期与 App 相同，
    // 其 deinit 永远不会被调用。系统会在 App 终止时自动清理资源。

    // MARK: - 公开方法

    func getBatteryInfo() -> BatteryInfo {
        let device = UIDevice.current
        return BatteryInfo(
            level: device.batteryLevel,
            state: device.batteryState,
            isLowPowerModeEnabled: ProcessInfo.processInfo.isLowPowerModeEnabled
        )
    }

    func getStorageInfo() -> StorageInfo {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            let totalSpace = (systemAttributes[.systemSize] as? NSNumber)?.int64Value ?? 0
            let freeSpace = (systemAttributes[.systemFreeSize] as? NSNumber)?.int64Value ?? 0
            let usedSpace = totalSpace - freeSpace
            return StorageInfo(totalSpace: totalSpace, availableSpace: freeSpace, usedSpace: usedSpace)
        } catch {
            LogManager.shared.error("获取存储空间信息失败: \(error.localizedDescription)")
            return StorageInfo(totalSpace: 0, availableSpace: 0, usedSpace: 0)
        }
    }

    func getMemoryInfo() -> MemoryInfo {
        return MemoryInfo(
            totalPhysicalMemory: Int64(ProcessInfo.processInfo.physicalMemory),
            appMemoryUsage: getAppMemoryUsage(),
            availableMemory: getAvailableMemory()
        )
    }

    func getDeviceInfo() -> DeviceInfo {
        let device = UIDevice.current
        return DeviceInfo(
            name: device.name, model: device.model, localizedModel: device.localizedModel,
            systemName: device.systemName, systemVersion: device.systemVersion,
            identifierForVendor: device.identifierForVendor?.uuidString,
            orientation: device.orientation, userInterfaceIdiom: device.userInterfaceIdiom
        )
    }

    /// 获取网络信息 (此方法现在是即时的，且不会阻塞任何线程)
    func getNetworkInfo() -> NetworkInfo {
        // 3. 直接从已存储的属性中读取网络状态
        return NetworkInfo(
            isWiFiAvailable: self.isWiFiAvailable,
            isCellularAvailable: self.isCellularAvailable,
            isNetworkReachable: self.isNetworkReachable
        )
    }

    func getSystemInfo() -> SystemInfo {
        let processInfo = ProcessInfo.processInfo
        return SystemInfo(
            iOSVersion: UIDevice.current.systemVersion,
            appVersion: AppUtility.appVersion,
            appBuild: AppUtility.appBuild,
            bundleIdentifier: AppUtility.appIdentifier,
            appName: AppUtility.appDisplayName,
            systemUptime: processInfo.systemUptime,
            timeZone: TimeZone.current,
            preferredLanguage: Locale.preferredLanguages.first ?? "en"
        )
    }

    // MARK: - 私有方法

    private func getAppMemoryUsage() -> Int64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        if kerr == KERN_SUCCESS {
            return Int64(info.resident_size)
        } else {
            LogManager.shared.error("获取应用内存使用量失败")
            return 0
        }
    }

    /// 获取可用内存 (优化后：包含空闲内存和非活跃内存)
    /// - Returns: 可用内存（字节）
    private func getAvailableMemory() -> Int64 {
        var info = vm_statistics64()
        var count = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                host_statistics64(mach_host_self(), HOST_VM_INFO64, $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let pageSize = Int64(vm_kernel_page_size)
            // 优化点：可用内存 = 空闲页 + 非活跃页
            let freePages = Int64(info.free_count)
            let inactivePages = Int64(info.inactive_count)
            return (freePages + inactivePages) * pageSize
        } else {
            LogManager.shared.error("获取可用内存失败")
            return 0
        }
    }

    // 4. 移除了旧的、会阻塞线程的 isNetworkReachable() 私有方法
}

// MARK: - 便捷扩展

extension SystemInfoManager {

    /// 获取所有系统信息的汇总
    /// (现在可以安全地在任何线程调用，不会引起阻塞)
    func getAllSystemInfo() -> [String: Any] {
        let batteryInfo = getBatteryInfo()
        let storageInfo = getStorageInfo()
        let memoryInfo = getMemoryInfo()
        let deviceInfo = getDeviceInfo()
        let networkInfo = getNetworkInfo()
        let systemInfo = getSystemInfo()

        return [
            "battery": [
                "level": batteryInfo.levelPercentage,
                "state": batteryInfo.stateDescription,
                "lowPowerMode": batteryInfo.isLowPowerModeEnabled
            ],
            "storage": [
                "total": storageInfo.totalSpaceFormatted,
                "available": storageInfo.availableSpaceFormatted,
                "used": storageInfo.usedSpaceFormatted,
                "usage": storageInfo.usagePercentageString
            ],
            "memory": [
                "total": memoryInfo.totalPhysicalMemoryFormatted,
                "appUsage": memoryInfo.appMemoryUsageFormatted,
                "available": memoryInfo.availableMemoryFormatted
            ],
            "device": [
                "name": deviceInfo.name,
                "model": deviceInfo.model,
                "type": deviceInfo.deviceTypeDescription,
                "orientation": deviceInfo.orientationDescription,
                "systemVersion": deviceInfo.systemVersion
            ],
            "network": [
                "status": networkInfo.statusDescription,
                "reachable": networkInfo.isNetworkReachable
            ],
            "system": [
                "iOSVersion": systemInfo.iOSVersion,
                "appVersion": systemInfo.appFullVersion,
                "bundleId": systemInfo.bundleIdentifier,
                "uptime": systemInfo.systemUptimeFormatted,
                "language": systemInfo.preferredLanguage
            ]
        ]
    }

    /// 生成系统信息报告字符串
    /// (现在可以安全地在任何线程调用，不会引起阻塞)
    func generateSystemReport() -> String {
        let batteryInfo = getBatteryInfo()
        let storageInfo = getStorageInfo()
        let memoryInfo = getMemoryInfo()
        let deviceInfo = getDeviceInfo()
        let networkInfo = getNetworkInfo()
        let systemInfo = getSystemInfo()

        var report = "=== 系统信息报告 ===\n\n"
        report += "📱 设备信息:\n"
        report += "  设备名称: \(deviceInfo.name)\n"
        report += "  设备型号: \(deviceInfo.model)\n"
        report += "  设备类型: \(deviceInfo.deviceTypeDescription)\n"
        report += "  系统版本: \(deviceInfo.systemName) \(deviceInfo.systemVersion)\n\n"

        report += "🔋 电池信息:\n"
        report += "  电量: \(batteryInfo.levelPercentage)\n"
        report += "  状态: \(batteryInfo.stateDescription)\n"
        report += "  低电量模式: \(batteryInfo.isLowPowerModeEnabled ? "已启用" : "未启用")\n\n"

        report += "💾 存储空间:\n"
        report += "  总容量: \(storageInfo.totalSpaceFormatted)\n"
        report += "  可用空间: \(storageInfo.availableSpaceFormatted)\n"
        report += "  已用空间: \(storageInfo.usedSpaceFormatted) (\(storageInfo.usagePercentageString))\n\n"

        report += "🧠 内存信息:\n"
        report += "  物理内存: \(memoryInfo.totalPhysicalMemoryFormatted)\n"
        report += "  应用内存: \(memoryInfo.appMemoryUsageFormatted)\n"
        report += "  可用内存: \(memoryInfo.availableMemoryFormatted)\n\n"

        report += "🌐 网络状态:\n"
        report += "  连接状态: \(networkInfo.statusDescription)\n\n"

        report += "📋 应用信息:\n"
        report += "  应用名称: \(systemInfo.appName)\n"
        report += "  应用版本: \(systemInfo.appFullVersion)\n"
        report += "  Bundle ID: \(systemInfo.bundleIdentifier)\n"
        report += "  系统运行时间: \(systemInfo.systemUptimeFormatted)\n"

        return report
    }
}
