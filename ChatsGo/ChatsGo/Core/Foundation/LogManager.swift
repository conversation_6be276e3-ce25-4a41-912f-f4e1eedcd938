import Foundation
import OSLog
import UIKit

/// 日志级别枚举
enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    
    var description: String {
        switch self {
        case .debug: return "DEBUG"
        case .info: return "INFO"
        case .warning: return "WARNING"
        case .error: return "ERROR"
        }
    }
    
    var osLogType: OSLogType {
        switch self {
        case .debug: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        }
    }
}

/// 日志输出目标
struct LogDestination: OptionSet {
    let rawValue: Int
    
    static let console = LogDestination(rawValue: 1 << 0)
    static let file = LogDestination(rawValue: 1 << 1)
    static let osLog = LogDestination(rawValue: 1 << 2)
    
    static let all: LogDestination = [.console, .file, .osLog]
    static let debugDefault: LogDestination = [.console, .file, .osLog]
    static let releaseDefault: LogDestination = [.file, .osLog]
}

/// 日志条目结构
struct LogEntry {
    let timestamp: Date
    let level: LogLevel
    let message: String
    let file: String
    let function: String
    let line: Int
    
    var formattedMessage: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        let timestampString = dateFormatter.string(from: timestamp)
        let fileName = (file as NSString).lastPathComponent
        return "[\(timestampString)] [\(level.description)] [\(fileName):\(line)] \(function) - \(message)"
    }
}

/// 日志管理器协议
protocol LogManagerProtocol {
    /// 记录日志
    func log(_ message: String, level: LogLevel, file: String, function: String, line: Int)
    
    /// 便捷方法
    func debug(_ message: String, file: String, function: String, line: Int)
    func info(_ message: String, file: String, function: String, line: Int)
    func warning(_ message: String, file: String, function: String, line: Int)
    func error(_ message: String, file: String, function: String, line: Int)
    
    /// 获取日志数据
    func getLogData() async -> Data?
    
    /// 清除所有日志
    func clearAllLogs() async
    
    /// 强制刷新缓冲区
    func flush() async
}

// MARK: - LogManagerProtocol 扩展，提供默认参数
extension LogManagerProtocol {
    func log(_ message: String, level: LogLevel) {
        log(message, level: level, file: #file, function: #function, line: #line)
    }
    
    func debug(_ message: String) {
        debug(message, file: #file, function: #function, line: #line)
    }
    
    func info(_ message: String) {
        info(message, file: #file, function: #function, line: #line)
    }
    
    func warning(_ message: String) {
        warning(message, file: #file, function: #function, line: #line)
    }
    
    func error(_ message: String) {
        error(message, file: #file, function: #function, line: #line)
    }
}

/// 高性能日志管理器
final class LogManager: LogManagerProtocol {
    /// 单例实例
    static let shared = LogManager()
    
    /// 配置
    private let minLogLevel: LogLevel
    private let destinations: LogDestination
    private let bufferSize: Int
    private let flushInterval: TimeInterval
    
    /// 文件相关
    private let logDirectory: URL
    private let defaultLogFileName = "app.log"
    private let maxLogSize: UInt64 = 5 * 1024 * 1024
    
    /// 性能优化相关
    private let logQueue = DispatchQueue(label: "com.chatsgo.logmanager", qos: .utility)
    private var logBuffer: [LogEntry] = []
    private var flushTimer: Timer?
    private let bufferLock = NSLock()
    
    /// OSLog实例
    private let osLogger = Logger(subsystem: AppUtility.appIdentifier, category: "app")

    /// 初始化
    private init(
        minLogLevel: LogLevel = .debug,
        destinations: LogDestination = {
            #if DEBUG
            return .debugDefault
            #else
            return .releaseDefault
            #endif
        }(),
        bufferSize: Int = 100,
        flushInterval: TimeInterval = 5.0
    ) {
        self.minLogLevel = minLogLevel
        self.destinations = destinations
        self.bufferSize = bufferSize
        self.flushInterval = flushInterval
        
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        logDirectory = documentsDirectory.appendingPathComponent("logs", isDirectory: true)
        
        setupLogManager()        
    }
    
    // 无需 deinit，因为是单例
    // deinit {
    //     flushTimer?.invalidate()
    //     flushSync()
    // }
    
    /// 设置日志管理器
    private func setupLogManager() {
        createLogDirectoryIfNeeded()
        setupFlushTimer()
    }
    
    /// 创建日志目录
    private func createLogDirectoryIfNeeded() {
        let fileManager = FileManager.default
        if !fileManager.fileExists(atPath: logDirectory.path) {
            do {
                try fileManager.createDirectory(at: logDirectory, withIntermediateDirectories: true)
            } catch {
                print("Failed to create log directory: \(error)")
            }
        }
    }
    
    /// 设置定时刷新
    private func setupFlushTimer() {
        flushTimer = Timer.scheduledTimer(withTimeInterval: flushInterval, repeats: true) { [weak self] _ in
            self?.flushSync()
        }
    }
    
    // MARK: - Public Methods
    
    /// 记录日志
    func log(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        guard level.rawValue >= minLogLevel.rawValue else { return }
        
        let entry = LogEntry(
            timestamp: Date(),
            level: level,
            message: message,
            file: file,
            function: function,
            line: line
        )
        
        logQueue.async { [weak self] in
            self?.processLogEntry(entry)
        }
    }
    
    /// 便捷方法
    func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .debug, file: file, function: function, line: line)
    }
    
    func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .info, file: file, function: function, line: line)
    }
    
    func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .warning, file: file, function: function, line: line)
    }
    
    func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .error, file: file, function: function, line: line)
    }
    
    /// 获取日志数据
    func getLogData() async -> Data? {
        await flush()
        
        let logFileURL = currentLogFileURL()
        
        do {
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                return try Data(contentsOf: logFileURL)
            }
        } catch {
            print("Failed to read log file: \(error)")
        }
        
        return nil
    }
    
    /// 清除所有日志
    func clearAllLogs() async {
        await withCheckedContinuation { continuation in
            logQueue.async { [weak self] in
                guard let self = self else { 
                    continuation.resume()
                    return 
                }
                
                bufferLock.lock()
                self.logBuffer.removeAll()
                bufferLock.unlock()
                
                let fileManager = FileManager.default
                do {
                    let logFiles = try fileManager.contentsOfDirectory(at: self.logDirectory, includingPropertiesForKeys: nil)
                    for fileURL in logFiles {
                        try fileManager.removeItem(at: fileURL)
                    }
                } catch {
                    print("Failed to clear logs: \(error)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 强制刷新缓冲区
    func flush() async {
        await withCheckedContinuation { continuation in
            logQueue.async { [weak self] in
                self?.flushBuffer()
                continuation.resume()
            }
        }
    }
    
    /// 定时刷新缓冲区（非阻塞调用）
    private func flushSync() {
        logQueue.async { [weak self] in
            self?.flushBuffer()
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理日志条目
    private func processLogEntry(_ entry: LogEntry) {
        if destinations.contains(.console) {
            print(entry.formattedMessage)
        }
        
        if destinations.contains(.osLog) {
            osLogger.log(level: entry.level.osLogType, "\(entry.message)")
        }
        
        if destinations.contains(.file) {
            bufferLock.lock()
            logBuffer.append(entry)
            let shouldFlush = logBuffer.count >= bufferSize
            bufferLock.unlock()
            
            if shouldFlush {
                flushBuffer()
            }
        }
    }
    
    /// 刷新缓冲区到文件
    private func flushBuffer() {
        bufferLock.lock()
        let entriesToFlush = logBuffer
        logBuffer.removeAll()
        bufferLock.unlock()
        
        guard !entriesToFlush.isEmpty else { return }
        
        checkAndRotateLogFileIfNeeded()
        
        writeEntriesToFile(entriesToFlush)
    }
    
    /// 批量写入日志条目到文件
    private func writeEntriesToFile(_ entries: [LogEntry]) {
        let logFileURL = currentLogFileURL()
        
        do {
            let logContent = entries.map { $0.formattedMessage + "\n" }.joined()
            
            if !FileManager.default.fileExists(atPath: logFileURL.path) {
                try "".write(to: logFileURL, atomically: true, encoding: .utf8)
            }
            
            let fileHandle = try FileHandle(forWritingTo: logFileURL)
            defer { fileHandle.closeFile() }
            
            fileHandle.seekToEndOfFile()
            if let data = logContent.data(using: .utf8) {
                fileHandle.write(data)
            }
        } catch {
            print("Failed to write log entries: \(error)")
        }
    }
    
    /// 获取当前日志文件URL
    private func currentLogFileURL() -> URL {
        return logDirectory.appendingPathComponent(defaultLogFileName)
    }
    
    /// 检查并轮转日志文件
    private func checkAndRotateLogFileIfNeeded() {
        let fileManager = FileManager.default
        let logFileURL = currentLogFileURL()
        
        guard fileManager.fileExists(atPath: logFileURL.path) else { return }
        
        do {
            let attributes = try fileManager.attributesOfItem(atPath: logFileURL.path)
            let fileSize = attributes[FileAttributeKey.size] as? UInt64 ?? 0
            
            if fileSize > maxLogSize {
                try rotateLogFile()
            }
        } catch {
            print("Failed to check log file size: \(error)")
        }
    }
    
    /// 轮转日志文件
    private func rotateLogFile() throws {
        let fileManager = FileManager.default
        let oldLogFileURL = currentLogFileURL()
        
        if fileManager.fileExists(atPath: oldLogFileURL.path) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
            let timestamp = dateFormatter.string(from: Date())
            let newLogFileURL = logDirectory.appendingPathComponent("app_\(timestamp).log")
            
            try fileManager.moveItem(at: oldLogFileURL, to: newLogFileURL)
        }
    }
}

// MARK: - 全局便捷函数
func logDebug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    LogManager.shared.debug(message, file: file, function: function, line: line)
}

func logInfo(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    LogManager.shared.info(message, file: file, function: function, line: line)
}

func logWarning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    LogManager.shared.warning(message, file: file, function: function, line: line)
}

func logError(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    LogManager.shared.error(message, file: file, function: function, line: line)
}

// MARK: - 使用示例
/*
 使用新的 async/await API 示例：
 
 // 获取日志数据
 Task {
     if let logData = await LogManager.shared.getLogData() {
         // 处理日志数据，比如发送到服务器
         print("Log data size: \(logData.count) bytes")
     }
 }
 
 // 清除所有日志
 Task {
     await LogManager.shared.clearAllLogs()
     print("All logs cleared")
 }
 
 // 强制刷新缓冲区
 Task {
     await LogManager.shared.flush()
     print("Buffer flushed")
 }
 */
