//
//  AppUtility.swift
//  WhatsAppMobile
//
//  Created by <PERSON><PERSON> on 22/05/2025.
//

import UIKit
import SafariServices

enum AppUtility {
    static var appName: String {
        return "ChatsGo"
    }

    static var appDisplayName: String {
        return Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String ?? appName
    }

    static var appIdentifier: String {
        return Bundle.main.bundleIdentifier ?? "com.itoolab.ChatsGo"
    }

    static var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }

    static var appBuild: String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }

    static var appVersionAndBuild: String {
        return "\(appVersion).\(appBuild)"
    }

    static func openUrl(urlString: String) {
        guard let url = URL(string: urlString) else { return }

        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }

    static func openAppSetting() {
        openUrl(urlString: UIApplication.openSettingsURLString)
    }

    /// 使用 SFSafariViewController 在应用内打开指定的 URL。
    ///
    /// 此方法会创建一个 SFSafariViewController 实例，然后从提供的视图控制器中模态显示它。
    ///
    /// - Parameters:
    ///   - urlString: 需要打开的 URL 字符串。如果 URL 无效，则此方法不执行任何操作。
    ///   - viewController: 用于呈现 SFSafariViewController 的视图控制器。
    static func openUrlWithSafari(urlString: String, from viewController: UIViewController) {
        guard let url = URL(string: urlString) else { return }

        let configuration = SFSafariViewController.Configuration()

        let safariVC = SFSafariViewController(url: url, configuration: configuration)
//        safariVC.preferredControlTintColor = UIColor.Theme.tintColor

        viewController.present(safariVC, animated: true, completion: nil)
    }

    /// 获取当前顶层视图控制器
    static func getTopViewController() -> UIViewController? {
        guard let window = getKeyWindow(),
              let rootViewController = window.rootViewController else {
            return nil
        }

        return getTopViewController(from: rootViewController)
    }
    
    static func getKeyWindow() -> UIWindow? {
        // Currently, the minimum supported version is iOS 14
        return UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }
    }

    /// 递归获取顶层视图控制器
    private static func getTopViewController(from viewController: UIViewController) -> UIViewController {
        if let presentedViewController = viewController.presentedViewController {
            return getTopViewController(from: presentedViewController)
        }

        if let navigationController = viewController as? UINavigationController,
           let topViewController = navigationController.topViewController {
            return getTopViewController(from: topViewController)
        }

        if let tabBarController = viewController as? UITabBarController,
           let selectedViewController = tabBarController.selectedViewController {
            return getTopViewController(from: selectedViewController)
        }

        return viewController
    }

    // MARK: - First Launch Detection

    private static let firstStartKey = "AppHasBeenOpened"
    
    /// 标记应用已被首次启动
    /// 这个方法会在 UserDefaults 中设置一个标志来表示应用已经被打开过
    static func markFirstStart() {
        if isFirstStart {
            UserDefaults.standard.set(true, forKey: firstStartKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 检查是否为首次启动应用
    /// - Returns: 如果是首次启动返回 true，否则返回 false
    static var isFirstStart: Bool {
        let hasBeenOpened = UserDefaults.standard.bool(forKey: firstStartKey)
        return !hasBeenOpened
    }
}
