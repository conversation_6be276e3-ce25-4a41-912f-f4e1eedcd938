//
//  ExpandableTouchAreaButton.swift
//  WhatsAppMobile
//
//  Created by Subo on 2023/7/25.
//

import UIKit

/// 扩展触摸区域的按钮 - 通过设置extraTouchArea来扩大按钮的可点击区域
class ExpandableTouchAreaButton: UIButton {
    /// 放大点击区域大小，默认为5
    var extraTouchArea: CGFloat = 5.0
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    private func commonInit() {
        adjustsImageWhenDisabled = false
    }
    
    /// 重写点击区域判断方法，实现热区扩大
    override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        // 获取当前按钮的bounds
        let bounds = self.bounds
        
        // 按照extraTouchArea扩大区域
        let expandedBounds = bounds.insetBy(dx: -extraTouchArea, dy: -extraTouchArea)
        
        // 判断点击是否在扩展的区域内
        return expandedBounds.contains(point)
    }
}
