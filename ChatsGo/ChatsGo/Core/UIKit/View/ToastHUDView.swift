//
//  FeedbackHUDView.swift
//  WhatsAppMobile
//
//  Created by Subo on 2025/05/25.
//

import UIKit
import SnapKit

/// 通用底部 HUD 提示视图
/// 用于在应用中显示临时消息提示，支持自动消失和动画效果
final class ToastHUDView: UIView {
    // MARK: - Properties
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .white
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.8)
        layer.cornerRadius = 6

        addSubview(messageLabel)
        
        messageLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(11)
        }
    }
    
    // MARK: - Public Methods

    static func show(message: String, in containerView: UIView) {
        containerView.subviews.filter { $0 is ToastHUDView }.forEach { $0.removeFromSuperview() }

        let hud = ToastHUDView()
        hud.show(message: message, in: containerView)
    }

    /// 显示HUD提示
    /// - Parameters:
    ///   - message: 提示消息
    ///   - in: 容器视图
    func show(message: String, in containerView: UIView) {
        messageLabel.text = message
        
        // 添加到容器视图
        containerView.addSubview(self)
        
        // 设置约束
        snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(containerView.safeAreaLayoutGuide.snp.bottom).offset(-80)
            make.left.greaterThanOrEqualToSuperview().offset(20)
            make.right.lessThanOrEqualToSuperview().offset(-20)
        }
        
        // 动画显示
        alpha = 0
        transform = CGAffineTransform(translationX: 0, y: 20)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.alpha = 1
            self.transform = .identity
        }
        
        // 自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.hide()
        }
    }
    
    /// 隐藏HUD提示
    private func hide() {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn) {
            self.alpha = 0
            self.transform = CGAffineTransform(translationX: 0, y: 20)
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
} 
