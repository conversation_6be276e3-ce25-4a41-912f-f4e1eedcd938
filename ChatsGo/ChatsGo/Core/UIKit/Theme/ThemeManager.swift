//
//  ThemeManager.swift
//  WhatsAppMobile
//
//  Created by Sub<PERSON> on 16/05/2025.
//

//
//  ThemeManager.swift
//

import UIKit
import SwiftUI

final class ThemeManager {
    static let shared = ThemeManager()
    
    // 主题颜色
    var tintColor: UIColor = .black
    var mainTextColor: UIColor = .black
    var secondaryTextColor: UIColor = .darkGray
    var auxiliaryTextColor: UIColor = .gray
    var backgroundColor: UIColor = .white
    var backgroundDarkColor: UIColor = .lightGray
    var borderColor: UIColor = .blue
    var separatorColor: UIColor = .lightGray
    var warnColor: UIColor = .orange
    var disableColor: UIColor = .gray
    var errorColor: UIColor = .red

    private init() { }
    
    func configTheme() {
        tintColor = UIColor(hex: "11C361")
        mainTextColor = UIColor(hex: "333333")
        secondaryTextColor = UIColor(hex: "666666")
        auxiliaryTextColor = UIColor(hex: "999999")
        backgroundColor = UIColor.white
        backgroundDarkColor = UIColor(hex: "F2F2F2")
        borderColor = UIColor(hex: "5E75FF")
        separatorColor = UIColor(hex: "E7E7E7")
        warnColor = UIColor(hex: "F1922E")
        disableColor = UIColor(hex: "BCBCBC")
        errorColor = UIColor(hex: "FF1E1E")
    }
}

extension UIColor {
    enum Theme {
        static var tintColor: UIColor {
            ThemeManager.shared.tintColor
        }
        
        static var mainTextColor: UIColor {
            ThemeManager.shared.mainTextColor
        }
        
        static var secondaryTextColor: UIColor {
            ThemeManager.shared.secondaryTextColor
        }
        
        static var auxiliaryTextColor: UIColor {
            ThemeManager.shared.auxiliaryTextColor
        }
        
        static var backgroundColor: UIColor {
            ThemeManager.shared.backgroundColor
        }
        
        static var backgroundDarkColor: UIColor {
            ThemeManager.shared.backgroundDarkColor
        }
        
        static var borderColor: UIColor {
            ThemeManager.shared.borderColor
        }
        
        static var separatorColor: UIColor {
            ThemeManager.shared.separatorColor
        }
        
        static var warnColor: UIColor {
            ThemeManager.shared.warnColor
        }
        
        static var disableColor: UIColor {
            ThemeManager.shared.disableColor
        }

        static var errorColor: UIColor {
            ThemeManager.shared.errorColor
        }
    }
}

// 适配SwiftUI的颜色扩展
extension Color {
    enum Theme {
        static var tintColor: Color {
            Color(UIColor.Theme.tintColor)
        }
        
        static var mainTextColor: Color {
            Color(UIColor.Theme.mainTextColor)
        }
        
        static var secondaryTextColor: Color {
            Color(UIColor.Theme.secondaryTextColor)
        }
        
        static var auxiliaryTextColor: Color {
            Color(UIColor.Theme.auxiliaryTextColor)
        }
        
        static var backgroundColor: Color {
            Color(UIColor.Theme.backgroundColor)
        }
        
        static var backgroundDarkColor: Color {
            Color(UIColor.Theme.backgroundDarkColor)
        }
        
        static var borderColor: Color {
            Color(UIColor.Theme.borderColor)
        }
        
        static var separatorColor: Color {
            Color(UIColor.Theme.separatorColor)
        }
        
        static var warnColor: Color {
            Color(UIColor.Theme.warnColor)
        }
        
        static var disableColor: Color {
            Color(UIColor.Theme.disableColor)
        }

        static var errorColor: Color {
            Color(UIColor.Theme.errorColor)
        }
    }
}


