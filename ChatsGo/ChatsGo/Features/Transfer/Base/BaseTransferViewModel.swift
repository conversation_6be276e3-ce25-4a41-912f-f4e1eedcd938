//
//  BaseTransferViewModel.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import Foundation
import Combine

/// Transfer模块基础ViewModel
/// 提供公共的响应式属性和方法
/// 遵循DRY原则，减少ViewModel间的代码重复
class BaseTransferViewModel: ObservableObject {
    
    // MARK: - Common Published Properties
    
    /// 是否正在加载
    @Published private(set) var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 是否可以继续操作
    @Published private(set) var canProceed: Bool = false
    
    // MARK: - Properties
    
    /// Combine订阅集合
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupInitialState()
        setupBindings()
    }
    
    deinit {
        cancellables.removeAll()
    }
    
    // MARK: - Setup Methods
    
    /// 设置初始状态
    /// 子类可以重写此方法来设置特定的初始状态
    func setupInitialState() {
        // 默认实现为空，子类可以重写
    }
    
    /// 设置数据绑定
    /// 子类可以重写此方法来设置特定的绑定逻辑
    func setupBindings() {
        // 默认实现为空，子类可以重写
    }
    
    // MARK: - Loading State Management
    
    /// 设置加载状态
    /// - Parameter loading: 是否正在加载
    func setLoading(_ loading: Bool) {
        DispatchQueue.main.async { [weak self] in
            self?.isLoading = loading
        }
    }
    
    /// 开始加载
    func startLoading() {
        setLoading(true)
        clearError()
    }
    
    /// 结束加载
    func stopLoading() {
        setLoading(false)
    }
    
    // MARK: - Error Handling
    
    /// 设置错误消息
    /// - Parameter message: 错误消息
    func setError(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            self?.errorMessage = message
            self?.isLoading = false
        }
    }
    
    /// 设置错误
    /// - Parameter error: 错误对象
    func setError(_ error: Error) {
        setError(error.localizedDescription)
    }
    
    /// 清除错误消息
    func clearError() {
        DispatchQueue.main.async { [weak self] in
            self?.errorMessage = nil
        }
    }
    
    // MARK: - Proceed State Management
    
    /// 设置是否可以继续
    /// - Parameter canProceed: 是否可以继续
    func setCanProceed(_ canProceed: Bool) {
        DispatchQueue.main.async { [weak self] in
            self?.canProceed = canProceed
        }
    }
    
    /// 更新继续状态
    /// 子类应该重写此方法来实现具体的逻辑
    func updateProceedState() {
        // 默认实现为空，子类应该重写
    }
    
    // MARK: - Async Operations
    
    /// 执行异步操作的通用方法
    /// - Parameter operation: 异步操作
    func performAsyncOperation<T>(_ operation: @escaping () async throws -> T) async -> Result<T, Error> {
        startLoading()
        
        do {
            let result = try await operation()
            stopLoading()
            return .success(result)
        } catch {
            setError(error)
            return .failure(error)
        }
    }
    
    /// 执行异步操作并处理结果
    /// - Parameters:
    ///   - operation: 异步操作
    ///   - onSuccess: 成功回调
    ///   - onFailure: 失败回调
    func performAsyncOperation<T>(
        _ operation: @escaping () async throws -> T,
        onSuccess: @escaping (T) -> Void = { _ in },
        onFailure: @escaping (Error) -> Void = { _ in }
    ) {
        Task {
            let result = await performAsyncOperation(operation)
            
            await MainActor.run {
                switch result {
                case .success(let value):
                    onSuccess(value)
                case .failure(let error):
                    onFailure(error)
                }
            }
        }
    }
}

// MARK: - BaseTransferViewModel + Validation

extension BaseTransferViewModel {
    
    /// 验证输入的通用方法
    /// - Parameter validators: 验证器数组
    /// - Returns: 验证结果
    func validate(using validators: [() -> ValidationResult]) -> ValidationResult {
        for validator in validators {
            let result = validator()
            if !result.isValid {
                return result
            }
        }
        return ValidationResult.valid
    }
    
    /// 验证结果
    struct ValidationResult {
        let isValid: Bool
        let errorMessage: String?
        
        static let valid = ValidationResult(isValid: true, errorMessage: nil)
        
        static func invalid(_ message: String) -> ValidationResult {
            return ValidationResult(isValid: false, errorMessage: message)
        }
    }
}

// MARK: - BaseTransferViewModel + Debouncing

extension BaseTransferViewModel {
    
    /// 创建防抖动的Publisher
    /// - Parameters:
    ///   - publisher: 原始Publisher
    ///   - delay: 延迟时间
    /// - Returns: 防抖动的Publisher
    func debounced<T>(_ publisher: Published<T>.Publisher, delay: TimeInterval = 0.5) -> AnyPublisher<T, Never> {
        return publisher
            .debounce(for: .seconds(delay), scheduler: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    /// 创建去重的Publisher
    /// - Parameter publisher: 原始Publisher
    /// - Returns: 去重的Publisher
    func removeDuplicates<T: Equatable>(_ publisher: Published<T>.Publisher) -> AnyPublisher<T, Never> {
        return publisher
            .removeDuplicates()
            .eraseToAnyPublisher()
    }
}

// MARK: - BaseTransferViewModel + Logging

extension BaseTransferViewModel {
    
    /// 记录调试信息
    /// - Parameter message: 调试消息
    func logDebug(_ message: String) {
        #if DEBUG
        print("[\(String(describing: type(of: self)))] \(message)")
        #endif
    }
    
    /// 记录错误信息
    /// - Parameter error: 错误对象
    func logError(_ error: Error) {
        #if DEBUG
        print("[\(String(describing: type(of: self)))] Error: \(error.localizedDescription)")
        #endif
    }
}
