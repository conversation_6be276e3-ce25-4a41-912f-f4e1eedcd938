//
//  ConnectionGuideViewController.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import UIKit
import SnapKit
import Combine

/// 连接新手机引导页面控制器
/// 遵循MVVM架构模式和SOLID原则
/// 继承BaseTransferViewController以减少代码重复
class ConnectionGuideViewController: BaseTransferViewController {
    
    // MARK: - UI Components
    
    /// 主滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .systemBackground
        scrollView.showsVerticalScrollIndicator = true
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    /// 内容容器视图
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 步骤容器视图
    private lazy var stepsContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// QR码扫描区域视图
    private lazy var qrCodeScanAreaView: QRCodeScanAreaView = {
        let view = QRCodeScanAreaView()
        view.isHidden = true
        view.onConnectToHotspotTapped = { [weak self] in
            self?.viewModel.connectToHotspot()
        }
        return view
    }()
    
    /// 底部指示器
    private lazy var bottomIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = .label
        view.layer.cornerRadius = 2
        return view
    }()
    
    // MARK: - Properties
    
    /// ViewModel
    private let viewModel: ConnectionGuideViewModel
    
    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    /// 步骤视图数组
    private var stepViews: [ConnectionGuideStepView] = []
    
    // MARK: - Initialization
    
    /// 初始化控制器
    /// - Parameter viewModel: 视图模型，支持依赖注入
    init(viewModel: ConnectionGuideViewModel = ConnectionGuideViewModel()) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        self.viewModel = ConnectionGuideViewModel()
        super.init(coder: coder)
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置页面标题
        pageTitle = "Connect new phone"

        setupSpecificUI()
        setupSpecificConstraints()
        bindViewModel()

        // 加载数据
        viewModel.loadSteps()
    }

    override func configureAppearance() {
        super.configureAppearance()
        // 可以在这里添加特定的外观配置
    }
    
    // MARK: - Private Methods
    
    private func setupSpecificUI() {
        // 添加特定于此页面的UI组件到contentContainer
        contentContainer.addSubview(scrollView)
        contentContainer.addSubview(bottomIndicator)

        scrollView.addSubview(contentView)
        contentView.addSubview(stepsContainer)
        contentView.addSubview(qrCodeScanAreaView)
    }
    
    private func setupSpecificConstraints() {
        // 滚动视图约束（相对于contentContainer）
        scrollView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.bottom.equalTo(bottomIndicator.snp.top).offset(-16)
        }
        
        // 内容视图约束
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        // 步骤容器约束
        stepsContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // QR码扫描区域约束
        qrCodeScanAreaView.snp.makeConstraints { make in
            make.top.equalTo(stepsContainer.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-24)
        }
        
        // 底部指示器约束
        bottomIndicator.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-8)
            make.centerX.equalToSuperview()
            make.width.equalTo(134)
            make.height.equalTo(4)
        }
    }

    private func bindViewModel() {
        // 绑定步骤数据
        viewModel.$steps
            .receive(on: DispatchQueue.main)
            .sink { [weak self] steps in
                self?.updateStepsUI(with: steps)
            }
            .store(in: &cancellables)

        // 绑定当前步骤索引
        viewModel.$currentStepIndex
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateCurrentStepUI()
            }
            .store(in: &cancellables)

        // 绑定QR码区域可见性
        viewModel.$shouldShowQRCodeArea
            .receive(on: DispatchQueue.main)
            .sink { [weak self] shouldShow in
                self?.updateQRCodeAreaVisibility(shouldShow)
            }
            .store(in: &cancellables)

        // 绑定错误消息
        viewModel.$errorMessage
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] errorMessage in
                self?.showErrorAlert(message: errorMessage)
            }
            .store(in: &cancellables)
    }

    private func updateStepsUI(with steps: [ConnectionGuideStep]) {
        // 清除现有的步骤视图
        stepViews.forEach { $0.removeFromSuperview() }
        stepViews.removeAll()
        stepsContainer.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 创建新的步骤视图
        for (index, step) in steps.enumerated() {
            let stepView = ConnectionGuideStepView()
            let isLastStep = (index == steps.count - 1)
            stepView.configure(with: step, isLastStep: isLastStep)

            stepViews.append(stepView)
            stepsContainer.addArrangedSubview(stepView)
        }
    }

    private func updateCurrentStepUI() {
        // 更新所有步骤视图的状态
        for (index, stepView) in stepViews.enumerated() {
            if index < viewModel.steps.count {
                let step = viewModel.steps[index]
                let isLastStep = (index == viewModel.steps.count - 1)
                stepView.configure(with: step, isLastStep: isLastStep)

                // 播放动画
                if step.isCurrent {
                    stepView.animateStepActivation()
                } else if step.isCompleted {
                    stepView.animateStepCompletion()
                }
            }
        }
    }

    private func updateQRCodeAreaVisibility(_ shouldShow: Bool) {
        UIView.animate(withDuration: 0.3) {
            self.qrCodeScanAreaView.isHidden = !shouldShow
            self.qrCodeScanAreaView.alpha = shouldShow ? 1.0 : 0.0
        }
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // MARK: - Overrides

    override func handleBackAction() {
        // 可以在这里添加特定的返回逻辑
        super.handleBackAction()
    }
}

// MARK: - ConnectionGuideViewController + Factory

extension ConnectionGuideViewController {

    /// 创建连接引导控制器的工厂方法
    /// - Returns: 配置好的连接引导控制器实例
    static func create() -> ConnectionGuideViewController {
        let viewModel = ConnectionGuideViewModel()
        return ConnectionGuideViewController(viewModel: viewModel)
    }

    /// 创建带有自定义步骤提供者的连接引导控制器
    /// - Parameter stepProvider: 自定义步骤提供者
    /// - Returns: 配置好的连接引导控制器实例
    static func create(with stepProvider: ConnectionGuideStepProviding) -> ConnectionGuideViewController {
        let viewModel = ConnectionGuideViewModel(stepProvider: stepProvider)
        return ConnectionGuideViewController(viewModel: viewModel)
    }
}

// MARK: - ConnectionGuideViewController + Navigation

extension ConnectionGuideViewController {

    /// 从指定的视图控制器推送到连接引导页面
    /// - Parameter from: 源视图控制器
    static func push(from viewController: UIViewController) {
        let connectionGuideVC = ConnectionGuideViewController.create()
        viewController.navigationController?.pushViewController(connectionGuideVC, animated: true)
    }

    /// 以模态方式呈现连接引导页面
    /// - Parameter from: 源视图控制器
    static func present(from viewController: UIViewController) {
        let connectionGuideVC = ConnectionGuideViewController.create()
        let navigationController = UINavigationController(rootViewController: connectionGuideVC)
        viewController.present(navigationController, animated: true)
    }
}
