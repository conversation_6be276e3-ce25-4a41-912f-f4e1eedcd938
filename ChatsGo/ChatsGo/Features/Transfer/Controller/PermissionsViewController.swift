//
//  PermissionsViewController.swift
//  ChatsGo
//
//  Created by Subo on 06/07/2025.
//

#if canImport(UIKit)
import UIKit
#endif
import SnapKit
import Combine

class PermissionsViewController: UIViewController {
    
    // MARK: - Properties
    private var viewModel: PermissionsViewModel!
    private var cancellables = Set<AnyCancellable>()
    
    // 权限数据源
    private var permissionsData: [(PermissionType, String, String, String)] = [
        (.camera, "camera", "Used to scan QR codes and connect two mobile phones for data transmission.", "camera.fill"),
        (.photoLibrary, "photo", "Used to retrieve photos or write photos to the current phone.", "photo.on.rectangle"),
        (.localNetwork, "Local Network", "Used for device discovery of surrounding devices to establish device connections.", "wifi")
    ]
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .singleLine
        tableView.separatorColor = UIColor.Theme.separatorColor
        tableView.backgroundColor = .white
        tableView.showsVerticalScrollIndicator = false
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 80
        tableView.register(PermissionTableViewCell.self, forCellReuseIdentifier: "PermissionCell")
        return tableView
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 120))
        view.backgroundColor = .white

        let maxWidth = view.frame.size.width - 32
        let descriptionLabel = UILabel()
        descriptionLabel.text = "In order to transfer data to your new phone we need the following permissions:"
        descriptionLabel.font = .systemFont(ofSize: 14, weight: .regular)
        descriptionLabel.textColor = UIColor.Theme.mainTextColor
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textAlignment = .left
        descriptionLabel.preferredMaxLayoutWidth = maxWidth
        view.addSubview(descriptionLabel)

        let seperatorLine = UIView()
        seperatorLine.backgroundColor = UIColor.Theme.separatorColor
        view.addSubview(seperatorLine)

        descriptionLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        seperatorLine.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(14)
            make.leading.trailing.equalTo(descriptionLabel)
            make.height.equalTo(1)
            make.bottom.equalToSuperview()
        }

        return view
    }()
    
    private lazy var footerView: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 200))
        view.backgroundColor = .white
        
        let statementView = UIView()
        statementView.backgroundColor = UIColor(hex: "ECFAF2")
        statementView.layer.cornerRadius = 8

        let padding: CGFloat = 16
        let innerPadding: CGFloat = 12
        let maxWidth = view.frame.size.width - padding * 2 - innerPadding * 2

        let statementTitleLabel = UILabel()
        statementTitleLabel.text = "statement:"
        statementTitleLabel.font = .systemFont(ofSize: 14, weight: .bold)
        statementTitleLabel.textColor = UIColor.Theme.mainTextColor
        statementTitleLabel.numberOfLines = 0
        statementTitleLabel.preferredMaxLayoutWidth = maxWidth

        let statementContentLabel = UILabel()
        statementContentLabel.text = "We will not store or upload any user's mobile phone data, so please feel free to use it."
        statementContentLabel.font = .systemFont(ofSize: 14, weight: .regular)
        statementContentLabel.textColor = UIColor.Theme.mainTextColor
        statementContentLabel.numberOfLines = 0
        statementContentLabel.preferredMaxLayoutWidth = maxWidth

        view.addSubview(statementView)
        statementView.addSubview(statementTitleLabel)
        statementView.addSubview(statementContentLabel)
        
        statementView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(padding)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        statementTitleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(innerPadding)
            make.right.equalToSuperview().offset(-innerPadding)
        }
        
        statementContentLabel.snp.makeConstraints { make in
            make.top.equalTo(statementTitleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(innerPadding)
            make.bottom.equalToSuperview().offset(-innerPadding)
        }
        
        return view
    }()
    
    private lazy var nextButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Next >", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemGreen
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupViewModel()
        bindViewModel()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        viewModel.checkInitialPermissions()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .white
        setupNavigation()
        
        view.addSubview(tableView)
        view.addSubview(nextButton)

        setupTableHeaderAndFooterView()
    }
    
    private func setupNavigation() {
        title = "Permission reminder"
        navigationController?.navigationBar.tintColor = .label
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(named: "nav_back_icon")?.withRenderingMode(.alwaysOriginal),
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )
    }

    private func setupTableHeaderAndFooterView() {
        let headerView = self.headerView
        let size = headerView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
        var frame = headerView.frame
        frame.size.height = size.height
        headerView.frame = frame
        tableView.tableHeaderView = headerView

        let footerView = self.footerView
        let footerSize = footerView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
        var footerFrame = footerView.frame
        footerFrame.size.height = footerSize.height
        footerView.frame = footerFrame
        tableView.tableFooterView = footerView
    }

    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(nextButton.snp.top).offset(-20)
        }
        
        nextButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-20)
            make.height.equalTo(50)
        }
    }
    
    private func setupViewModel() {
        viewModel = PermissionsViewModel()
    }
    
    private func bindViewModel() {
        // 监听权限状态变化
        viewModel.$permissionStatuses
            .receive(on: DispatchQueue.main)
            .sink { [weak self] statuses in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
        
        // 监听是否可以继续
        viewModel.$canProceed
            .receive(on: DispatchQueue.main)
            .sink { [weak self] canProceed in
                self?.updateNextButton(enabled: canProceed)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Updates
    private func updateNextButton(enabled: Bool) {
        nextButton.isEnabled = enabled
        nextButton.alpha = enabled ? 1.0 : 0.5
        nextButton.backgroundColor = enabled ? .systemGreen : .systemGray
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func nextButtonTapped() {
        // 进入下一步流程
        print("All permissions granted, proceeding to next step...")
        // TODO: 导航到下一个页面
    }
}

// MARK: - UITableViewDataSource
extension PermissionsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return permissionsData.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PermissionCell", for: indexPath) as! PermissionTableViewCell
        
        let permission = permissionsData[indexPath.row]
        let status = viewModel.permissionStatuses[permission.0] ?? .notDetermined
        
        cell.configure(
            type: permission.0,
            title: permission.1,
            description: permission.2,
            iconName: permission.3,
            status: status
        )
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension PermissionsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let permission = permissionsData[indexPath.row]
        let currentStatus = viewModel.permissionStatuses[permission.0] ?? .notDetermined
        
        // 只有在未授权或被拒绝的情况下才触发权限请求
        if currentStatus != .granted {
            viewModel.requestPermission(for: permission.0)
        }
    }
}

/*
 使用示例和集成指南:
 
 1. 基本使用:
 
 let permissionsVC = PermissionsViewController()
 navigationController?.pushViewController(permissionsVC, animated: true)
 
 2. 集成到 Transfer 流程:
 
 // 在 TransferCoordinator 中
 func presentPermissions() {
     let permissionsVC = PermissionsViewController()
     presentingViewController.navigationController?.pushViewController(permissionsVC, animated: true)
 }
 
 3. 支持更多权限:
 
 // 修改 permissionsData 数组添加更多权限项
 private var permissionsData: [(PermissionType, String, String, String)] = [
     (.camera, "相机", "描述", "camera.fill"),
     (.photoLibrary, "照片", "描述", "photo.on.rectangle"),
     (.localNetwork, "本地网络", "描述", "wifi"),
     (.contacts, "通讯录", "描述", "person.2.fill"),
     (.calendar, "日历", "描述", "calendar"),
     // 更多权限...
 ]
 */
