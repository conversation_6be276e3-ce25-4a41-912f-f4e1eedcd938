//
//  TransferProtocols.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import Foundation
import UIKit

// MARK: - Coordinator Protocols

/// 统一传输协调器协议
protocol UnifiedTransferCoordinatorProtocol: AnyObject {
    func startTransferFlow()
    func startConnectionGuide()
    func startPermissionsFlow()
    func endAllFlows()
}

/// 传输协调器协议
protocol TransferCoordinatorProtocol: AnyObject {
    func startTransferFlow()
    func endTransferFlow()
}

/// 无线传输协调器协议
protocol WirelessTransferCoordinatorProtocol: AnyObject {
    func startConnectionGuide()
    func startDataTransfer()
    func endTransferFlow()
}

// MARK: - Service Protocols

/// 权限服务协议
protocol PermissionServiceProtocol: AnyObject {
    func checkAllPermissions() async -> [PermissionType: PermissionStatus]
    func requestPermission(for type: PermissionType) async -> PermissionRequestResult
    func openSettings()
}

/// 传输服务协议
protocol TransferServiceProtocol: AnyObject {
    func startTransfer(with configuration: TransferConfiguration) async throws
    func stopTransfer()
    func getTransferStatus() -> TransferStatus
}

/// 本地网络权限检查协议
protocol LocalNetworkPermissionChecking: AnyObject {
    func checkPermission() async -> PermissionStatus
    func requestPermission() async -> PermissionRequestResult
}

// MARK: - Data Provider Protocols

/// 连接引导步骤提供者协议
protocol ConnectionGuideStepProviding: AnyObject {
    func provideSteps() -> [ConnectionGuideStep]
}

/// 权限数据提供者协议
protocol PermissionDataProviding: AnyObject {
    func getRequiredPermissions() -> [PermissionType]
    func getPermissionDisplayInfo(for type: PermissionType) -> PermissionDisplayInfo
}

// MARK: - View Protocols

/// 底部弹窗内容协议
protocol BottomSheetContentProtocol: AnyObject {
    var contentHeight: CGFloat { get }
    var onDismiss: (() -> Void)? { get set }
}

/// 步骤视图协议
protocol StepViewProtocol: AnyObject {
    func configure(with step: ConnectionGuideStep, isLastStep: Bool)
    func animateStepCompletion()
    func animateStepActivation()
}

/// 权限单元格协议
protocol PermissionCellProtocol: AnyObject {
    func configure(type: PermissionType, title: String, description: String, iconName: String, status: PermissionStatus)
}

// MARK: - ViewModel Protocols

/// 基础ViewModel协议
protocol BaseViewModelProtocol: ObservableObject {
    var isLoading: Bool { get }
    var errorMessage: String? { get set }
    var canProceed: Bool { get }
    
    func setLoading(_ loading: Bool)
    func setError(_ message: String)
    func clearError()
}

/// 连接引导ViewModel协议
protocol ConnectionGuideViewModelProtocol: BaseViewModelProtocol {
    var steps: [ConnectionGuideStep] { get }
    var currentStepIndex: Int { get }
    var shouldShowQRCodeArea: Bool { get }
    
    func loadSteps()
    func proceedToNextStep()
    func goToPreviousStep()
    func resetGuide()
    func completeGuide()
}

/// 权限ViewModel协议
protocol PermissionsViewModelProtocol: BaseViewModelProtocol {
    var permissionStatuses: [PermissionType: PermissionStatus] { get }
    
    func checkInitialPermissions()
    func requestPermission(for type: PermissionType)
    func openSettings()
}

// MARK: - Navigation Protocols

/// 导航处理协议
protocol NavigationHandling: AnyObject {
    func pushViewController(_ viewController: UIViewController, animated: Bool)
    func popViewController(animated: Bool)
    func presentViewController(_ viewController: UIViewController, animated: Bool)
    func dismissViewController(animated: Bool)
}

/// 模态呈现协议
protocol ModalPresentationProtocol: AnyObject {
    func presentModally(from viewController: UIViewController)
    func dismissModally(completion: (() -> Void)?)
}

// MARK: - Callback Protocols

/// 传输回调协议
protocol TransferCallbackProtocol: AnyObject {
    func transferDidStart()
    func transferDidProgress(_ progress: Float)
    func transferDidComplete()
    func transferDidFail(with error: Error)
}

/// 权限回调协议
protocol PermissionCallbackProtocol: AnyObject {
    func permissionDidChange(_ type: PermissionType, status: PermissionStatus)
    func allPermissionsGranted()
    func permissionDenied(_ type: PermissionType)
}

// MARK: - Configuration Protocols

/// 传输配置协议
protocol TransferConfigurationProtocol {
    var transferType: TransferType { get }
    var dataTypes: [DataType] { get }
    var targetDevice: DeviceInfo? { get }
}

/// UI配置协议
protocol UIConfigurationProtocol {
    var primaryColor: UIColor { get }
    var secondaryColor: UIColor { get }
    var backgroundColor: UIColor { get }
    var cornerRadius: CGFloat { get }
}

// MARK: - Factory Protocols

/// 视图控制器工厂协议
protocol ViewControllerFactoryProtocol: AnyObject {
    func createConnectionGuideViewController() -> ConnectionGuideViewController
    func createPermissionsViewController() -> PermissionsViewController
}

/// 协调器工厂协议
protocol CoordinatorFactoryProtocol: AnyObject {
    func createUnifiedTransferCoordinator(
        navigationController: UINavigationController?,
        presentingViewController: UIViewController?
    ) -> UnifiedTransferCoordinatorProtocol
}

// MARK: - Supporting Types

/// 传输类型
enum TransferType {
    case androidToiPhone
    case iPhoneToAndroid
    case iPhoneToiPhone
    case androidToAndroid
}

/// 数据类型
enum DataType {
    case photos
    case videos
    case contacts
    case messages
    case apps
    case music
    case documents
    case other
}

/// 传输状态
enum TransferStatus {
    case idle
    case preparing
    case connecting
    case transferring(progress: Float)
    case completed
    case failed(Error)
    case cancelled
}

/// 设备信息
struct DeviceInfo {
    let name: String
    let type: DeviceType
    let identifier: String
}

/// 设备类型
enum DeviceType {
    case iPhone
    case android
    case unknown
}

/// 权限显示信息
struct PermissionDisplayInfo {
    let title: String
    let description: String
    let iconName: String
    let isRequired: Bool
}

/// 传输配置
struct TransferConfiguration: TransferConfigurationProtocol {
    let transferType: TransferType
    let dataTypes: [DataType]
    let targetDevice: DeviceInfo?
}
