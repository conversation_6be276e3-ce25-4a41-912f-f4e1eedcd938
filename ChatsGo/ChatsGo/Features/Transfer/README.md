# WirelessTransfer Module

## 概述

WirelessTransfer模块实现了"Connect new phone"引导页面，遵循SOLID原则和MVVM架构模式。该模块提供了一个清晰、用户友好的界面来引导用户完成设备连接过程。

## 架构设计

### SOLID原则的应用

1. **单一职责原则 (SRP)**
   - `ConnectionGuideStep`: 只负责表示引导步骤数据
   - `ConnectionGuideViewModel`: 只负责引导流程的业务逻辑
   - `ConnectionGuideStepView`: 只负责显示单个步骤
   - `QRCodeScanAreaView`: 只负责QR码扫描区域的UI

2. **开闭原则 (OCP)**
   - 通过协议和工厂方法支持扩展
   - 可以轻松添加新的步骤类型或自定义步骤提供者

3. **里氏替换原则 (LSP)**
   - 所有实现`ConnectionGuideStepProviding`协议的类都可以互换使用
   - 协调器可以被任何实现`WirelessTransferCoordinatorProtocol`的类替换

4. **接口隔离原则 (ISP)**
   - 协议设计精简，只包含必要的方法
   - 不同的组件只依赖它们需要的接口

5. **依赖倒置原则 (DIP)**
   - ViewModel依赖于抽象的`ConnectionGuideStepProviding`协议
   - 支持依赖注入，便于测试和扩展

## 文件结构

```
ChatsGo/ChatsGo/Core/WirelessTransfer/
├── Models/
│   └── ConnectionGuideStep.swift          # 数据模型
├── ViewModels/
│   └── ConnectionGuideViewModel.swift     # 业务逻辑
├── Views/
│   ├── ConnectionGuideStepView.swift      # 步骤视图组件
│   └── QRCodeScanAreaView.swift          # QR码扫描区域
├── Controllers/
│   └── ConnectionGuideViewController.swift # 主视图控制器
├── Coordinators/
│   └── WirelessTransferCoordinator.swift  # 流程协调器
└── README.md                              # 文档
```

## 使用方法

### 基本使用

```swift
// 1. 简单推送到连接引导页面
ConnectionGuideViewController.push(from: self)

// 2. 模态呈现连接引导页面
ConnectionGuideViewController.present(from: self)

// 3. 使用协调器管理流程
let coordinator = WirelessTransferCoordinator.create(with: navigationController)
coordinator.startConnectionGuide()
```

### 自定义步骤提供者

```swift
// 创建自定义步骤提供者
class CustomStepProvider: ConnectionGuideStepProviding {
    func provideSteps() -> [ConnectionGuideStep] {
        return [
            ConnectionGuideStep(stepNumber: 1, title: "Custom Step 1", description: "Custom description"),
            // ... 更多自定义步骤
        ]
    }
}

// 使用自定义步骤提供者
let customProvider = CustomStepProvider()
let viewController = ConnectionGuideViewController.create(with: customProvider)
```

### 集成到现有Transfer模块

```swift
// 在现有的TransferCoordinator中集成
class TransferCoordinator {
    private var wirelessTransferCoordinator: WirelessTransferCoordinatorProtocol?
    
    func showConnectionGuide() {
        if let navigationController = presentingViewController?.navigationController {
            wirelessTransferCoordinator = WirelessTransferCoordinator.create(with: navigationController)
            wirelessTransferCoordinator?.startConnectionGuide()
        }
    }
}
```

## 组件详解

### ConnectionGuideStep (数据模型)

```swift
struct ConnectionGuideStep {
    let stepNumber: Int
    let title: String
    let description: String
    var isCompleted: Bool
    var isCurrent: Bool
}
```

**特性:**
- 不可变的核心数据
- 支持状态变化（完成/当前）
- 提供工厂方法创建默认步骤

### ConnectionGuideViewModel (业务逻辑)

```swift
class ConnectionGuideViewModel: ObservableObject {
    @Published private(set) var steps: [ConnectionGuideStep]
    @Published private(set) var currentStepIndex: Int
    @Published private(set) var canProceed: Bool
    @Published private(set) var shouldShowQRCodeArea: Bool
}
```

**特性:**
- 响应式属性绑定
- 步骤流程管理
- QR码区域可见性控制
- 支持依赖注入

### ConnectionGuideViewController (视图控制器)

**特性:**
- MVVM架构
- 自适应布局
- 流畅动画效果
- 工厂方法创建

### 自定义视图组件

1. **ConnectionGuideStepView**: 显示单个引导步骤
2. **QRCodeScanAreaView**: QR码扫描区域和热点连接选项

## 测试

### 单元测试

```swift
// 测试ViewModel的业务逻辑
class ConnectionGuideViewModelTests: XCTestCase {
    func testProceedToNextStep() {
        // Given
        viewModel.loadSteps()
        
        // When
        viewModel.proceedToNextStep()
        
        // Then
        XCTAssertEqual(viewModel.currentStepIndex, 1)
    }
}
```

### 测试覆盖

- ViewModel业务逻辑测试
- 数据模型测试
- 协议实现测试
- 响应式属性测试

## 扩展指南

### 添加新步骤

1. 创建自定义`ConnectionGuideStepProviding`实现
2. 定义新的步骤数据
3. 注入到ViewModel中

### 自定义UI

1. 继承现有视图组件
2. 实现自定义样式
3. 保持接口兼容性

### 集成新功能

1. 遵循现有的协议设计
2. 使用协调器模式管理导航
3. 保持SOLID原则

## 最佳实践

1. **依赖注入**: 使用协议和工厂方法
2. **测试驱动**: 先写测试，再实现功能
3. **响应式编程**: 使用Combine进行数据绑定
4. **组件化**: 保持组件的独立性和可复用性
5. **文档化**: 为公共接口提供清晰的文档

## 性能考虑

1. **内存管理**: 使用weak引用避免循环引用
2. **UI更新**: 确保在主线程更新UI
3. **动画优化**: 使用适当的动画时长和缓动函数
4. **资源释放**: 在适当的时机清理资源

## 未来改进

1. **国际化支持**: 添加多语言支持
2. **主题适配**: 支持深色模式和自定义主题
3. **无障碍功能**: 改进VoiceOver支持
4. **性能监控**: 添加性能指标收集
