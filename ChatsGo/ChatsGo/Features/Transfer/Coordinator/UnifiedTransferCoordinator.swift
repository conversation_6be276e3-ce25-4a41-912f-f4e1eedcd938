//
//  UnifiedTransferCoordinator.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import Foundation
import UIKit

/// 统一的传输协调器协议
/// 整合所有传输相关的导航流程
protocol UnifiedTransferCoordinatorProtocol: AnyObject {
    /// 开始传输流程（底部弹窗方式）
    func startTransferFlow()
    
    /// 开始连接引导流程（全屏方式）
    func startConnectionGuide()
    
    /// 开始权限检查流程
    func startPermissionsFlow()
    
    /// 结束所有传输流程
    func endAllFlows()
}

/// 统一传输协调器
/// 负责管理整个Transfer模块的所有导航流程
/// 遵循单一职责原则：只负责导航协调
class UnifiedTransferCoordinator: UnifiedTransferCoordinatorProtocol {
    
    // MARK: - Properties
    
    /// 导航控制器（用于全屏导航）
    private weak var navigationController: UINavigationController?
    
    /// 呈现视图控制器（用于弹窗）
    private weak var presentingViewController: UIViewController?
    
    /// 传输协调器（处理底部弹窗流程）
    private var transferCoordinator: TransferCoordinatorProtocol?
    
    /// 无线传输协调器（处理连接引导）
    private var wirelessTransferCoordinator: WirelessTransferCoordinatorProtocol?
    
    /// 当前活动的流程类型
    private var currentFlowType: TransferFlowType?
    
    // MARK: - Flow Types
    
    private enum TransferFlowType {
        case bottomSheetTransfer    // 底部弹窗传输流程
        case connectionGuide        // 连接引导流程
        case permissions           // 权限检查流程
    }
    
    // MARK: - Initialization
    
    init(navigationController: UINavigationController?, presentingViewController: UIViewController?) {
        self.navigationController = navigationController
        self.presentingViewController = presentingViewController
    }
    
    // MARK: - UnifiedTransferCoordinatorProtocol
    
    func startTransferFlow() {
        guard let presentingViewController = presentingViewController else { return }
        
        // 创建并启动传输协调器
        transferCoordinator = TransferCoordinator(presentingViewController: presentingViewController)
        transferCoordinator?.startTransferFlow()
        currentFlowType = .bottomSheetTransfer
    }
    
    func startConnectionGuide() {
        guard let navigationController = navigationController else { return }
        
        // 创建并启动无线传输协调器
        wirelessTransferCoordinator = WirelessTransferCoordinator.create(with: navigationController)
        wirelessTransferCoordinator?.startConnectionGuide()
        currentFlowType = .connectionGuide
    }
    
    func startPermissionsFlow() {
        guard let navigationController = navigationController else { return }
        
        // 推送到权限页面
        let permissionsVC = PermissionsViewController()
        navigationController.pushViewController(permissionsVC, animated: true)
        currentFlowType = .permissions
    }
    
    func endAllFlows() {
        // 结束所有活动的流程
        transferCoordinator?.endTransferFlow()
        wirelessTransferCoordinator?.endTransferFlow()
        
        // 清理引用
        transferCoordinator = nil
        wirelessTransferCoordinator = nil
        currentFlowType = nil
    }
}

// MARK: - UnifiedTransferCoordinator + Factory

extension UnifiedTransferCoordinator {
    
    /// 创建统一传输协调器
    /// - Parameters:
    ///   - navigationController: 导航控制器
    ///   - presentingViewController: 呈现视图控制器
    /// - Returns: 配置好的协调器实例
    static func create(
        navigationController: UINavigationController?,
        presentingViewController: UIViewController?
    ) -> UnifiedTransferCoordinatorProtocol {
        return UnifiedTransferCoordinator(
            navigationController: navigationController,
            presentingViewController: presentingViewController
        )
    }
}

// MARK: - UnifiedTransferCoordinator + Flow Management

extension UnifiedTransferCoordinator {
    
    /// 检查是否有活动的流程
    var hasActiveFlow: Bool {
        return currentFlowType != nil
    }
    
    /// 获取当前流程类型
    var activeFlowType: String? {
        switch currentFlowType {
        case .bottomSheetTransfer:
            return "Bottom Sheet Transfer"
        case .connectionGuide:
            return "Connection Guide"
        case .permissions:
            return "Permissions"
        case .none:
            return nil
        }
    }
    
    /// 强制结束特定类型的流程
    /// - Parameter flowType: 要结束的流程类型
    func endSpecificFlow(_ flowType: TransferFlowType) {
        switch flowType {
        case .bottomSheetTransfer:
            transferCoordinator?.endTransferFlow()
            transferCoordinator = nil
        case .connectionGuide:
            wirelessTransferCoordinator?.endTransferFlow()
            wirelessTransferCoordinator = nil
        case .permissions:
            navigationController?.popToRootViewController(animated: true)
        }
        
        if currentFlowType == flowType {
            currentFlowType = nil
        }
    }
}

// MARK: - Usage Example

/*
 使用示例：
 
 class SomeViewController: UIViewController {
     private var transferCoordinator: UnifiedTransferCoordinatorProtocol?
     
     override func viewDidLoad() {
         super.viewDidLoad()
         
         transferCoordinator = UnifiedTransferCoordinator.create(
             navigationController: navigationController,
             presentingViewController: self
         )
     }
     
     @IBAction func showTransferOptions(_ sender: UIButton) {
         transferCoordinator?.startTransferFlow()
     }
     
     @IBAction func showConnectionGuide(_ sender: UIButton) {
         transferCoordinator?.startConnectionGuide()
     }
     
     @IBAction func checkPermissions(_ sender: UIButton) {
         transferCoordinator?.startPermissionsFlow()
     }
 }
 */
