//
//  TransferCoordinator.swift
//  ChatsGo
//
//  Created by AI Assistant on 02/07/2025.
//

import UIKit

/**
 Transfer 功能协调器
 
 负责管理 Transfer 模块的导航流程和状态转换，包括：
 - Phone Transfer 入口界面
 - 传输方向选择界面
 - 数据类型选择界面
 - 底部弹窗的展示和切换
 
 - Note: 遵循 Coordinator 模式，解耦视图控制器之间的依赖关系
 */
class TransferCoordinator: TransferCoordinatorProtocol {

    // MARK: - Properties
    
    /// 当前展示的底部弹窗控制器
    private var currentBottomSheetViewController: BottomSheetViewController?
    
    /// 呈现弹窗的源视图控制器
    private weak var presentingViewController: UIViewController?
    
    // MARK: - Transfer Flow States
    
    /// Transfer 流程状态
    private enum TransferFlowState {
        case directionSelection     // 方向选择界面
        case dataTypeSelection      // 数据类型选择界面
    }
    
    /// 当前流程状态
    private var currentState: TransferFlowState = .directionSelection
    
    // MARK: - Initialization
    
    /// 初始化协调器
    /// - Parameter presentingViewController: 用于呈现弹窗的源视图控制器
    init(presentingViewController: UIViewController) {
        self.presentingViewController = presentingViewController
    }
    
    // MARK: - Public Methods
    
    /// 开始 Transfer 流程，显示方向选择界面
    func startTransferFlow() {
        showDirectionSelectionBottomSheet()
    }
    
    /// 结束 Transfer 流程，关闭所有弹窗
    func endTransferFlow() {
        currentBottomSheetViewController?.dismiss(animated: true) { [weak self] in
            self?.currentBottomSheetViewController = nil
            self?.currentState = .directionSelection
        }
    }
    
    // MARK: - Private Methods - Direction Selection
    
    private func showDirectionSelectionBottomSheet() {
        let directionSelectionView = DirectionSelectionBottomSheetView()
        
        // 设置回调
        directionSelectionView.onDismiss = { [weak self] in
            self?.handleDirectionSelectionDismiss()
        }
        
        directionSelectionView.onAndroidToiPhoneSelected = { [weak self] in
            self?.handleAndroidToiPhoneSelected()
        }
        
        directionSelectionView.oniPhoneToAndroidSelected = { [weak self] in
            self?.handleiPhoneToAndroidSelected()
        }
        
        // 始终创建新的弹窗，确保状态正确
        presentBottomSheet(with: directionSelectionView)
        currentState = .directionSelection
    }
    
    private func handleDirectionSelectionDismiss() {
        print("关闭方向选择弹窗")
        // 直接清理状态，不调用 endTransferFlow() 避免重复关闭
        currentBottomSheetViewController = nil
        currentState = .directionSelection
    }
    
    private func handleAndroidToiPhoneSelected() {
        print("选择了 Android to iPhone，显示数据类型选择")
        showDataTypeSelectionBottomSheet()
    }
    
    private func handleiPhoneToAndroidSelected() {
        print("iPhone to Android 功能即将推出")
        
        // 显示 Toast 提示
        guard let presentingVC = presentingViewController else { return }
        ToastHUDView.show(message: "iPhone to Android 功能即将推出，敬请期待", in: presentingVC.view)
    }
    
    // MARK: - Private Methods - Data Type Selection
    
    private func showDataTypeSelectionBottomSheet() {
        let dataTypeSelectionView = DataTypeSelectionBottomSheetView()
        
        // 设置回调
        dataTypeSelectionView.onBack = { [weak self] in
            self?.handleDataTypeSelectionBack()
        }
        
        dataTypeSelectionView.onBasicDataSelected = { [weak self] in
            self?.handleBasicDataSelected()
        }
        
        dataTypeSelectionView.onOtherDataSelected = { [weak self] in
            self?.handleOtherDataSelected()
        }
        
        // 使用推入动画
        currentBottomSheetViewController?.pushContentView(dataTypeSelectionView)
        currentState = .dataTypeSelection
    }
    
    private func handleDataTypeSelectionBack() {
        print("从数据类型选择返回到方向选择")
        currentBottomSheetViewController?.popContentView()
        currentState = .directionSelection
    }
    
    private func handleBasicDataSelected() {
        print("选择了 Basic data，开始传输流程")
        // TODO: 实现实际的传输逻辑
        endTransferFlow()
    }
    
    private func handleOtherDataSelected() {
        print("选择了 Other data，开始传输流程")
        // TODO: 实现实际的传输逻辑
        endTransferFlow()
    }
    
    // MARK: - Private Methods - Bottom Sheet Management
    
    /// 呈现新的底部弹窗
    /// - Parameter contentView: 弹窗内容视图
    private func presentBottomSheet(with contentView: BottomSheetContentProtocol) {
        guard let presentingVC = presentingViewController else {
            print("错误：缺少用于呈现弹窗的源视图控制器")
            return
        }
        
        // 如果已有弹窗，先关闭并清理状态
        if let currentBottomSheet = currentBottomSheetViewController {
            currentBottomSheet.dismiss(animated: false)
            currentBottomSheetViewController = nil
        }
        
        // 创建新的底部弹窗，根据设计稿要求不显示拖拽指示器
        let bottomSheetVC = BottomSheetViewController(contentView: contentView, showDragIndicator: false)
        currentBottomSheetViewController = bottomSheetVC
        
        // 呈现弹窗
        presentingVC.present(bottomSheetVC, animated: true)
    }
    

}

// MARK: - TransferCoordinatorProtocol

/**
 Transfer 协调器协议
 
 定义 Transfer 协调器的基本接口，方便测试和解耦
 */
protocol TransferCoordinatorProtocol {
    /// 开始 Transfer 流程
    func startTransferFlow()
    
    /// 结束 Transfer 流程
    func endTransferFlow()
} 
