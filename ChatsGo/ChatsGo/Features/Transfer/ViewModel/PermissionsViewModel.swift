//
//  PermissionsViewModel.swift
//  ChatsGo
//
//  Created by Subo on 06/07/2025.
//

import Foundation
import Combine
import AVFoundation
import Photos

#if canImport(UIKit)
import UIKit
#endif

class PermissionsViewModel {
    
    // MARK: - Dependencies
    private let localNetworkChecker = LocalNetworkPermissionChecker()

    // @Published 属性会自动通知订阅者（ViewController）其值的变化
    @Published private(set) var permissionStatuses: [PermissionType: PermissionStatus] = [:]
    @Published private(set) var canProceed: Bool = false

    // UserDefaults Keys
    private enum UserDefaultsKeys {
        static let hasTriggeredLocalNetworkPermission = "ChatsGo_HasTriggeredLocalNetworkPermission"
    }

    init() {
        // 初始化时不立即检查权限，等待viewController调用
    }

    func checkInitialPermissions() {
        Task {
            let asyncStatuses = await checkAllPermissions()
            DispatchQueue.main.async {
                self.permissionStatuses = asyncStatuses
                self.updateProceedButtonState()
            }
        }
    }

    func requestPermission(for type: PermissionType) {
        switch type {
        case .camera:
            requestCameraAccess { [weak self] _ in
                self?.checkInitialPermissions()
            }
        case .photoLibrary:
            requestPhotoLibraryAccess { [weak self] _ in
                self?.checkInitialPermissions()
            }
        case .localNetwork:
            // 使用 LocalNetworkPermissionChecker 处理本地网络权限
            Task {
                // 标记已经触发过本地网络权限弹窗
                UserDefaults.standard.set(true, forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
                
                // 使用专门的检查器处理本地网络权限
                let _ = await self.localNetworkChecker.checkAndRequestPermission(showAlert: true)
                
                DispatchQueue.main.async {
                    self.checkInitialPermissions()
                }
            }
        }
    }

    func openSettings() {
        #if canImport(UIKit)
        guard let url = URL(string: UIApplication.openSettingsURLString),
              UIApplication.shared.canOpenURL(url) else {
            return
        }
        UIApplication.shared.open(url)
        #endif
    }

    // MARK: - Private Methods

    private func checkAllPermissions() async -> [PermissionType: PermissionStatus] {
        var statuses: [PermissionType: PermissionStatus] = [:]
        statuses[.camera] = checkCameraPermission()
        statuses[.photoLibrary] = checkPhotoLibraryPermission()
        statuses[.localNetwork] = await checkLocalNetworkPermission()
        return statuses
    }

    private func updateProceedButtonState() {
        // 当所有权限都为 .granted 时，"Next" 按钮才可用
        canProceed = permissionStatuses.allSatisfy { $0.value == .granted }
    }

    // MARK: - Permission Checkers

    private func checkCameraPermission() -> PermissionStatus {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: return .granted
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }

    private func checkPhotoLibraryPermission() -> PermissionStatus {
        switch PHPhotoLibrary.authorizationStatus(for: .readWrite) {
        case .authorized, .limited: return .granted
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }

    private func checkLocalNetworkPermission() async -> PermissionStatus {
        let hasTriggered = UserDefaults.standard.bool(forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
        
        if !hasTriggered {
            return .notDetermined
        } else {
            // 使用 LocalNetworkPermissionChecker 检查实际的权限状态
            let hasPermission = await localNetworkChecker.checkPermission()
            return hasPermission ? .granted : .denied
        }
    }

    // MARK: - Permission Requesters

    private func requestCameraAccess(completion: @escaping (Bool) -> Void) {
        AVCaptureDevice.requestAccess(for: .video) { granted in
            DispatchQueue.main.async {
                completion(granted)
            }
        }
    }

    private func requestPhotoLibraryAccess(completion: @escaping (Bool) -> Void) {
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
            DispatchQueue.main.async {
                completion(status == .authorized || status == .limited)
            }
        }
    }
}
