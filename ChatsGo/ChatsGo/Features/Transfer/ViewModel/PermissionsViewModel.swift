//
//  PermissionsViewModel.swift
//  ChatsGo
//
//  Created by Subo on 06/07/2025.
//

import Foundation
import Combine

#if canImport(UIKit)
import UIKit
#endif

class PermissionsViewModel: ObservableObject {

    // MARK: - Dependencies
    private let permissionService: PermissionServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // @Published 属性会自动通知订阅者（ViewController）其值的变化
    @Published private(set) var permissionStatuses: [PermissionType: PermissionStatus] = [:]
    @Published private(set) var canProceed: Bool = false
    @Published private(set) var isLoading: Bool = false
    @Published private(set) var errorMessage: String?

    // 必需的权限列表
    private let requiredPermissions: [PermissionType] = [.camera, .photoLibrary, .localNetwork]

    init(permissionService: PermissionServiceProtocol = PermissionService.shared) {
        self.permissionService = permissionService
        setupNotificationObservers()
    }

    deinit {
        cancellables.removeAll()
    }

    // MARK: - Public Methods

    func checkInitialPermissions() {
        Task {
            await loadPermissions()
        }
    }

    func requestPermission(for type: PermissionType) {
        Task {
            await performPermissionRequest(for: type)
        }
    }

    func openSettings() {
        permissionService.openSettings()
    }

    // MARK: - Private Methods

    private func setupNotificationObservers() {
        // 监听本地网络权限更新通知（向后兼容）
        NotificationCenter.default.publisher(for: .localNetworkPermissionUpdated)
            .compactMap { $0.userInfo?["status"] as? PermissionStatus }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                self?.permissionStatuses[.localNetwork] = status
                self?.updateProceedButtonState()
            }
            .store(in: &cancellables)
    }

    @MainActor
    private func loadPermissions() async {
        isLoading = true
        errorMessage = nil

        do {
            let statuses = await permissionService.checkAllPermissions()
            permissionStatuses = statuses
            updateProceedButtonState()
        } catch {
            errorMessage = "检查权限时发生错误: \(error.localizedDescription)"
        }

        isLoading = false
    }

    @MainActor
    private func performPermissionRequest(for type: PermissionType) async {
        isLoading = true
        errorMessage = nil

        do {
            let result = await permissionService.requestPermission(for: type)

            switch result {
            case .granted:
                permissionStatuses[type] = .granted
            case .denied:
                permissionStatuses[type] = .denied
                errorMessage = "权限被拒绝，请在设置中手动开启"
            case .restricted:
                permissionStatuses[type] = .denied
                errorMessage = "权限受限，无法使用此功能"
            case .notDetermined:
                permissionStatuses[type] = .notDetermined
            case .error(let error):
                errorMessage = error.localizedDescription
            }

            updateProceedButtonState()
        } catch {
            errorMessage = "请求权限时发生错误: \(error.localizedDescription)"
        }

        isLoading = false
    }

    private func updateProceedButtonState() {
        // 当所有必需权限都为 .granted 时，"Next" 按钮才可用
        canProceed = requiredPermissions.allSatisfy { type in
            permissionStatuses[type] == .granted
        }
    }
}
