//
//  ConnectionGuideViewModel.swift
//  ChatsGo
//
//  Created by AI Assistant on 07/07/2025.
//

import Foundation
import Combine

/// 连接引导页面的ViewModel
/// 遵循MVVM架构模式，负责管理引导步骤的业务逻辑
class ConnectionGuideViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 引导步骤列表
    @Published private(set) var steps: [ConnectionGuideStep] = []
    
    /// 当前步骤索引
    @Published private(set) var currentStepIndex: Int = 0
    
    /// 是否可以继续下一步
    @Published private(set) var canProceed: Bool = false
    
    /// 是否显示QR码扫描区域
    @Published private(set) var shouldShowQRCodeArea: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Dependencies (依赖倒置原则)
    
    private let stepProvider: ConnectionGuideStepProviding
    
    // MARK: - Initialization
    
    /// 初始化ViewModel
    /// - Parameter stepProvider: 步骤数据提供者，支持依赖注入
    init(stepProvider: ConnectionGuideStepProviding = DefaultConnectionGuideStepProvider()) {
        self.stepProvider = stepProvider
        setupInitialState()
    }
    
    // MARK: - Public Methods
    
    /// 加载引导步骤
    func loadSteps() {
        steps = stepProvider.provideSteps()
        updateCurrentStep()
        updateQRCodeVisibility()
    }
    
    /// 进入下一步
    func proceedToNextStep() {
        guard canProceed, currentStepIndex < steps.count - 1 else {
            return
        }
        
        // 标记当前步骤为已完成
        steps[currentStepIndex].isCompleted = true
        steps[currentStepIndex].isCurrent = false
        
        // 移动到下一步
        currentStepIndex += 1
        updateCurrentStep()
        updateQRCodeVisibility()
    }
    
    /// 返回上一步
    func goToPreviousStep() {
        guard currentStepIndex > 0 else {
            return
        }
        
        // 重置当前步骤状态
        steps[currentStepIndex].isCurrent = false
        
        // 移动到上一步
        currentStepIndex -= 1
        steps[currentStepIndex].isCompleted = false
        updateCurrentStep()
        updateQRCodeVisibility()
    }
    
    /// 重置引导流程
    func resetGuide() {
        currentStepIndex = 0
        loadSteps()
    }
    
    /// 完成引导流程
    func completeGuide() {
        // 标记最后一步为已完成
        if currentStepIndex < steps.count {
            steps[currentStepIndex].isCompleted = true
            steps[currentStepIndex].isCurrent = false
        }
        
        // 这里可以添加完成引导后的逻辑
        // 例如：导航到下一个页面、保存完成状态等
    }
    
    /// 手动连接到热点
    func connectToHotspot() {
        // 这里实现连接到热点的逻辑
        // 可以调用系统设置或显示连接指引
        print("Connecting to hotspot...")
    }
    
    // MARK: - Private Methods
    
    private func setupInitialState() {
        loadSteps()
    }
    
    private func updateCurrentStep() {
        // 更新当前步骤状态
        for (index, _) in steps.enumerated() {
            steps[index].isCurrent = (index == currentStepIndex)
        }
        
        // 更新是否可以继续
        updateCanProceed()
    }
    
    private func updateCanProceed() {
        // 根据当前步骤决定是否可以继续
        // 这里可以添加更复杂的逻辑，比如检查某些条件是否满足
        canProceed = currentStepIndex < steps.count - 1
    }
    
    private func updateQRCodeVisibility() {
        // 在第3步显示QR码扫描区域
        shouldShowQRCodeArea = (currentStepIndex == 2) // STEP 3 (索引为2)
    }
}

// MARK: - ConnectionGuideStepProviding Protocol

/// 连接引导步骤提供者协议
/// 遵循依赖倒置原则，允许不同的步骤数据源实现
protocol ConnectionGuideStepProviding {
    /// 提供引导步骤
    /// - Returns: 引导步骤数组
    func provideSteps() -> [ConnectionGuideStep]
}

// MARK: - DefaultConnectionGuideStepProvider

/// 默认的连接引导步骤提供者
/// 实现ConnectionGuideStepProviding协议
class DefaultConnectionGuideStepProvider: ConnectionGuideStepProviding {
    
    func provideSteps() -> [ConnectionGuideStep] {
        return ConnectionGuideStep.createDefaultSteps()
    }
}
