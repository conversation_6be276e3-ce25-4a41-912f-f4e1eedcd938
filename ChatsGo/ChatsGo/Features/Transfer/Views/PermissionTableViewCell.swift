//
//  PermissionTableViewCell.swift
//  ChatsGo
//
//  Created by Subo on 07/07/2025.
//

import UIKit
import SnapKit

// MARK: - Permission Table View Cell
class PermissionTableViewCell: UITableViewCell {
    
    private var permissionType: PermissionType?
    
    // UI Components
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.tintColor = .systemGreen
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.Theme.mainTextColor
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.Theme.secondaryTextColor
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textAlignment = .right
        return label
    }()
    
    private lazy var checkmarkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "checkmark.circle.fill")
        imageView.tintColor = .systemGreen
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var warningView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemRed
        view.layer.cornerRadius = 12
        view.isHidden = true
        
        let label = UILabel()
        label.text = "Permission not granted"
        label.font = .systemFont(ofSize: 10, weight: .regular)
        label.textColor = .white
        label.textAlignment = .center
        
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(6)
        }
        
        return view
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .white
        selectionStyle = .default
        
        contentView.addSubview(iconImageView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(descriptionLabel)
        contentView.addSubview(statusLabel)
        contentView.addSubview(checkmarkImageView)
        contentView.addSubview(warningView)
    }
    
    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.size.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(statusLabel.snp.leading).offset(-16)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.lessThanOrEqualTo(statusLabel.snp.leading).offset(-16)
            make.bottom.lessThanOrEqualToSuperview().offset(-12)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(80)
        }
        
        checkmarkImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        
        warningView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
            make.width.greaterThanOrEqualTo(100)
        }
    }
    
    // MARK: - Configuration
    func configure(type: PermissionType, title: String, description: String, iconName: String, status: PermissionStatus) {
        self.permissionType = type
        
        // 设置图标
        iconImageView.image = UIImage(systemName: iconName)
        
        // 设置文本
        titleLabel.text = title
        descriptionLabel.text = description
        
        // 更新状态
        updateStatus(status)
    }
    
    private func updateStatus(_ status: PermissionStatus) {
        switch status {
        case .granted:
            statusLabel.isHidden = true
            checkmarkImageView.isHidden = false
            warningView.isHidden = true
        case .denied:
            statusLabel.isHidden = true
            checkmarkImageView.isHidden = true
            warningView.isHidden = false
        case .notDetermined:
            statusLabel.text = "Authorize>"
            statusLabel.textColor = .systemBlue
            statusLabel.isHidden = false
            checkmarkImageView.isHidden = true
            warningView.isHidden = true
        }
    }
}
