//
//  DataTypeSelectionBottomSheetView.swift
//  ChatsGo
//
//  Created by AI Assistant on 02/07/2025.
//

import UIKit
import SnapKit

/**
 数据类型选择底部弹窗内容视图
 
 展示数据类型选择界面，包含：
 - 返回按钮和标题
 - Basic data 选项（图片、视频、音乐、联系人、应用等）
 - Other data 选项（文本消息、通话记录）
 
 - Note: 实现 BottomSheetContentProtocol 协议以集成到通用弹窗系统
 */
class DataTypeSelectionBottomSheetView: UIView {
    
    // MARK: - Properties
    
    /// 关闭回调
    var onDismiss: (() -> Void)?
    
    /// 返回上一级回调
    var onBack: (() -> Void)?
    
    /// 选择 Basic data 回调
    var onBasicDataSelected: (() -> Void)?
    
    /// 选择 Other data 回调
    var onOtherDataSelected: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 顶部导航容器
    private lazy var navigationContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 返回按钮
    private lazy var backButton: UIButton = {
        let button = ExpandableTouchAreaButton(type: .custom)
        button.setImage(UIImage(named: "nav_back_icon"), for: .normal)
        button.tintColor = .label
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Select a transfer direction"
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.Theme.mainTextColor
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    /// 选项容器视图
    private lazy var optionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// Basic data 选项
    private lazy var basicDataOption: DataTypeOptionView = {
        let option = DataTypeOptionView(
            icon: "transfer_basic_data_icon",
            title: "Basic data",
            subtitle: "Transfer photos, videos, music, contacts, apps, etc."
        )
        option.onTapped = { [weak self] in
            self?.onBasicDataSelected?()
        }
        return option
    }()
    
    /// Other data 选项
    private lazy var otherDataOption: DataTypeOptionView = {
        let option = DataTypeOptionView(
            icon: "transfer_other_data_icon",
            title: "Other data",
            subtitle: "Transfer text messages, call logs."
        )
        option.onTapped = { [weak self] in
            self?.onOtherDataSelected?()
        }
        return option
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {        
        // 添加子视图
        addSubview(navigationContainer)
        addSubview(optionsContainer)
        
        // 导航容器子视图
        navigationContainer.addSubview(backButton)
        navigationContainer.addSubview(titleLabel)
        
        // 选项容器子视图
        optionsContainer.addSubview(basicDataOption)
        optionsContainer.addSubview(otherDataOption)
    }
    
    private func setupConstraints() {
        navigationContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(30)
        }

        backButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(backButton.snp.trailing).offset(8)
            make.trailing.equalToSuperview().offset(-16)
        }

        var bottomPadding: CGFloat = 0
        if let window = AppUtility.getKeyWindow() {
            bottomPadding = window.safeAreaInsets.bottom
        }
        optionsContainer.snp.makeConstraints { make in
            make.top.equalTo(navigationContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-bottomPadding)
        }

        basicDataOption.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.greaterThanOrEqualTo(66)
        }
        
        otherDataOption.snp.makeConstraints { make in
            make.top.equalTo(basicDataOption.snp.bottom).offset(16)
            make.leading.trailing.equalTo(basicDataOption)
            make.height.greaterThanOrEqualTo(66)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Action Methods
    
    @objc private func backButtonTapped() {
        onBack?()
    }
}

// MARK: - BottomSheetContentProtocol

extension DataTypeSelectionBottomSheetView: BottomSheetContentProtocol {
    var contentView: UIView {
        return self
    }
    
    var preferredContentHeight: CGFloat {
        // 确保视图已经完成布局计算
        contentView.setNeedsLayout()
        contentView.layoutIfNeeded()
        
        // 使用 systemLayoutSizeFitting 计算准确的高度
        let targetSize = CGSize(
            width: UIScreen.main.bounds.width, 
            height: UIView.layoutFittingCompressedSize.height
        )
        
        let calculatedSize = contentView.systemLayoutSizeFitting(
            targetSize,
            withHorizontalFittingPriority: .required,
            verticalFittingPriority: .fittingSizeLevel
        )
        
        return calculatedSize.height
    }
}

// MARK: - DataTypeOptionView

/**
 数据类型选项视图
 
 显示单个数据类型选项，包含图标、标题和描述
 */
class DataTypeOptionView: UIView {
    
    // MARK: - Properties
    
    /// 点击回调
    var onTapped: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 图标标签
    private lazy var iconImageView = UIImageView()

    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = UIColor.Theme.mainTextColor
        label.textAlignment = .left
        label.numberOfLines = 1
        return label
    }()
    
    /// 描述标签
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor(hex: "BBBBBB").withAlphaComponent(0.8)
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()

    private lazy var verLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#ECFAF2")
        return view
    }()

    // MARK: - Initialization
    
    init(icon: String, title: String, subtitle: String) {
        super.init(frame: .zero)
        
        iconImageView.image = UIImage(named: icon)
        titleLabel.text = title
        descriptionLabel.text = subtitle
        
        setupUI()
        setupConstraints()
        setupInteraction()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
        setupInteraction()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 12
        
        // 添加子视图
        addSubview(iconImageView)
        addSubview(verLine)
        addSubview(titleLabel)
        addSubview(descriptionLabel)
    }
    
    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(28)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(26)
        }

        verLine.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(22)
            make.centerY.equalToSuperview()
            make.top.bottom.equalToSuperview().inset(13)
            make.width.equalTo(1)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(verLine.snp.trailing).offset(15)
            make.trailing.equalToSuperview().offset(-10)
            make.top.equalToSuperview().offset(8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.trailing.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.bottom.lessThanOrEqualToSuperview().offset(-8)
        }
    }
    
    private func setupInteraction() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(viewTapped))
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Action Methods
    
    @objc private func viewTapped() {
        // 添加点击反馈动画
        UIView.animate(withDuration: 0.1, delay: 0, options: [.allowUserInteraction]) {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                self.transform = .identity
            } completion: { _ in
                self.onTapped?()
            }
        }
    }
} 
