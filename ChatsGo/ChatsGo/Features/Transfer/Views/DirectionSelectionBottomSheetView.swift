//
//  DirectionSelectionBottomSheetView.swift
//  ChatsGo
//
//  Created by AI Assistant on 02/07/2025.
//

import UIKit
import SnapKit

/**
 传输方向选择底部弹窗内容视图
 
 展示传输方向选择界面，包含：
 - 返回按钮和标题
 - Android to iPhone 选项
 - iPhone to Android 选项 (即将推出)
 
 - Note: 实现 BottomSheetContentProtocol 协议以集成到通用弹窗系统
 */
class DirectionSelectionBottomSheetView: UIView {
    
    // MARK: - Properties
    
    /// 关闭回调
    var onDismiss: (() -> Void)?
    
    /// 返回上一级回调
    var onBack: (() -> Void)?
    
    /// 选择 Android to iPhone 回调
    var onAndroidToiPhoneSelected: (() -> Void)?
    
    /// 选择 iPhone to Android 回调
    var oniPhoneToAndroidSelected: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 顶部导航容器
    private lazy var navigationContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 关闭按钮
    private lazy var closeButton: UIButton = {
        let button = ExpandableTouchAreaButton(type: .custom)
        button.setImage(UIImage(named: "close_icon"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Select a transfer direction"
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.Theme.mainTextColor
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    /// 选项容器视图
    private lazy var optionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// Android to iPhone 选项
    private lazy var androidToiPhoneOption: TransferDirectionOptionView = {
        let option = TransferDirectionOptionView(
            icon: "android_to_iphone1",
            arrowIcon: "→",
            targetIcon: "android_to_iphone2",
            title: "Android to iPhone"
        )
        option.arrowIconLabel.textColor = UIColor(hex: "#3590FB")
        option.onTapped = { [weak self] in
            self?.onAndroidToiPhoneSelected?()
        }
        return option
    }()
    
    /// iPhone to Android 选项
    private lazy var iPhoneToAndroidOption: TransferDirectionOptionView = {
        let option = TransferDirectionOptionView(
            icon: "iphone_to_android1",
            arrowIcon: "→",
            targetIcon: "iphone_to_android2",
            title: "iPhone to Android",
            subtitle: "Coming soon..."
        )
        option.arrowIconLabel.textColor = UIColor(hex: "#7D6FF8")
        option.onTapped = { [weak self] in
            self?.oniPhoneToAndroidSelected?()
            self?.onDismiss?()
        }
        return option
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {        
        // 添加子视图
        addSubview(navigationContainer)
        addSubview(optionsContainer)
        
        // 导航容器子视图
        navigationContainer.addSubview(closeButton)
        navigationContainer.addSubview(titleLabel)
        
        // 选项容器子视图
        optionsContainer.addSubview(androidToiPhoneOption)
        optionsContainer.addSubview(iPhoneToAndroidOption)
    }
    
    private func setupConstraints() {
        navigationContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(30)
        }
        
        closeButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(closeButton.snp.trailing).offset(8)
            make.trailing.equalToSuperview().offset(-16)
        }

        var bottomPadding: CGFloat = 0
        if let window = AppUtility.getKeyWindow() {
            bottomPadding = window.safeAreaInsets.bottom
        }
        optionsContainer.snp.makeConstraints { make in
            make.top.equalTo(navigationContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-bottomPadding)
        }
        
        androidToiPhoneOption.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(56)
        }
        
        iPhoneToAndroidOption.snp.makeConstraints { make in
            make.top.equalTo(androidToiPhoneOption.snp.bottom).offset(16)
            make.leading.trailing.equalTo(androidToiPhoneOption)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Action Methods
    
    @objc private func closeButtonTapped() {
        onDismiss?()
    }
}

// MARK: - BottomSheetContentProtocol

extension DirectionSelectionBottomSheetView: BottomSheetContentProtocol {
    var contentView: UIView {
        return self
    }
    
    var preferredContentHeight: CGFloat {
        // 确保视图已经完成布局计算
        contentView.setNeedsLayout()
        contentView.layoutIfNeeded()
        
        // 使用 systemLayoutSizeFitting 计算准确的高度
        let targetSize = CGSize(
            width: UIScreen.main.bounds.width, 
            height: UIView.layoutFittingCompressedSize.height
        )
        
        let calculatedSize = contentView.systemLayoutSizeFitting(
            targetSize,
            withHorizontalFittingPriority: .required,
            verticalFittingPriority: .fittingSizeLevel
        )
        
        return calculatedSize.height
    }
}

// MARK: - TransferDirectionOptionView

/**
 传输方向选项视图
 
 显示单个传输方向选项，包含图标、箭头、目标图标和标题
 */
class TransferDirectionOptionView: UIView {
    
    // MARK: - Properties
    
    /// 点击回调
    var onTapped: (() -> Void)?

    // MARK: - UI Components
    
    /// 源图标标签
    private lazy var sourceImageView = UIImageView()

    /// 箭头图标标签
    lazy var arrowIconLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        return label
    }()
    
    /// 目标图标标签
    private lazy var targetImageView = UIImageView()

    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        label.textAlignment = .left
        label.numberOfLines = 1
        return label
    }()
    
    /// 副标题标签
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor(hex: "BBBBBB").withAlphaComponent(0.8)
        return label
    }()

    private lazy var verLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#ECFAF2")
        return view
    }()

    // MARK: - Initialization
    
    init(icon: String, arrowIcon: String, targetIcon: String, title: String, subtitle: String? = nil) {
        super.init(frame: .zero)
        
        sourceImageView.image = UIImage(named: icon)
        arrowIconLabel.text = arrowIcon
        targetImageView.image = UIImage(named: targetIcon)
        titleLabel.text = title
        subtitleLabel.text = subtitle
        subtitleLabel.isHidden = subtitle == nil
        
        setupUI()
        setupConstraints()
        setupInteraction()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
        setupInteraction()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 8

        // 添加子视图
        addSubview(sourceImageView)
        addSubview(arrowIconLabel)
        addSubview(targetImageView)
        addSubview(verLine)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
    }
    
    private func setupConstraints() {
        sourceImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        arrowIconLabel.snp.makeConstraints { make in
            make.leading.equalTo(sourceImageView.snp.trailing).offset(4)
            make.centerY.equalToSuperview()
            make.width.equalTo(12)
        }
        
        targetImageView.snp.makeConstraints { make in
            make.leading.equalTo(arrowIconLabel.snp.trailing).offset(4)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        verLine.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(targetImageView.snp.trailing).offset(5)
            make.width.equalTo(1)
            make.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(verLine.snp.trailing).offset(16)
            make.trailing.equalToSuperview().offset(-12)

            if subtitleLabel.isHidden {
                make.centerY.equalToSuperview()
            } else {
                make.top.equalToSuperview().offset(10)
            }
        }
        
        if !subtitleLabel.isHidden {
            subtitleLabel.snp.makeConstraints { make in
                make.leading.trailing.equalTo(titleLabel)
                make.top.equalTo(titleLabel.snp.bottom).offset(4)
            }
        }
    }
    
    private func setupInteraction() {
        isUserInteractionEnabled = true

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(viewTapped))
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Action Methods
    
    @objc private func viewTapped() {
        // 添加点击反馈动画
        UIView.animate(withDuration: 0.1, delay: 0, options: [.allowUserInteraction]) {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                self.transform = .identity
            } completion: { _ in
                self.onTapped?()
            }
        }
    }
} 
