//
//  BottomSheetViewController.swift
//  ChatsGo
//
//  Created by AI Assistant on 02/07/2025.
//

import UIKit
import SnapKit

/**
 通用底部半弹窗控制器
 
 该类提供了一个可复用的底部半弹窗实现，支持：
 - 自定义内容视图
 - 流畅的弹出/消失动画
 - 背景遮罩点击关闭
 - 拖拽手势关闭
 - 自适应内容高度
 - 可选的拖拽指示器
 
 - Note: 遵循 SOLID 原则，通过 BottomSheetContentProtocol 解耦内容实现
 */
class BottomSheetViewController: UIViewController {
    
    // MARK: - Properties
    
    /// 内容视图的协议实现
    private var contentView: BottomSheetContentProtocol
    
    /// 是否显示拖拽指示器
    private var showDragIndicator: Bool

    /// 动画持续时间
    private let animationDuration: TimeInterval = 0.3
    
    /// 内容视图的最大高度比例 (相对于屏幕高度)
    private let maxHeightRatio: CGFloat = 0.9
    
    /// 约束引用，用于动画（SnapKit）
    private var mainStackTopConstraint: Constraint!
    private var mainStackHeightConstraint: Constraint!
    
    /// 内容视图栈，用于支持推拉动画
    private var contentViewStack: [BottomSheetContentProtocol] = []
    
    // MARK: - UI Components
    
    /// 背景遮罩视图
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        view.alpha = 0
        return view
    }()
    
    /// 主要的垂直 StackView，包含拖拽指示器和内容容器
    private lazy var mainStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.backgroundColor = UIColor(hex: "#ECFAF2")
        stackView.layer.cornerRadius = 12
        stackView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        stackView.clipsToBounds = true
        return stackView
    }()
    
    /// 顶部拖拽指示器
    private lazy var dragIndicatorView: UIView = {
        let view = UIView()
        view.backgroundColor = .tertiaryLabel
        view.layer.cornerRadius = 2
        return view
    }()
    
    /// 拖拽指示器的容器视图
    private lazy var dragIndicatorContainerView: UIView = {
        let view = UIView()
        view.addSubview(dragIndicatorView)
        
        dragIndicatorView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.equalTo(36)
            make.height.equalTo(4)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        return view
    }()
    
    /// 容器视图 - 包含内容的主容器
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // MARK: - Initialization
    
    /// 初始化方法
    /// - Parameters:
    ///   - contentView: 符合 BottomSheetContentProtocol 的内容视图
    ///   - showDragIndicator: 是否显示拖拽指示器，默认为 true
    init(contentView: BottomSheetContentProtocol, showDragIndicator: Bool = true) {
        self.contentView = contentView
        self.showDragIndicator = showDragIndicator
        super.init(nibName: nil, bundle: nil)
        self.modalPresentationStyle = .overFullScreen
        self.modalTransitionStyle = .crossDissolve
        self.contentViewStack = [contentView]
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupGestures()
        
        // 设置内容视图的回调
        contentView.onDismiss = { [weak self] in
            self?.dismiss(animated: true)
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        animatePresentation()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        view.backgroundColor = .clear
        
        // 添加子视图
        view.addSubview(backgroundView)
        view.addSubview(mainStackView)
        
        // 根据 showDragIndicator 决定是否添加拖拽指示器
        if showDragIndicator {
            mainStackView.addArrangedSubview(dragIndicatorContainerView)
        }
        
        // 添加内容容器
        mainStackView.addArrangedSubview(containerView)
        
        // 将内容视图添加到容器中
        let contentUIView = contentView.contentView
        containerView.addSubview(contentUIView)
        contentUIView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupConstraints() {
        // 背景遮罩
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 计算内容高度，但不超过最大高度
        let contentHeight = contentView.preferredContentHeight
        let dragIndicatorHeight: CGFloat = showDragIndicator ? 28 : 0 // 8 + 4 + 16
        let maxHeight = view.bounds.height * maxHeightRatio
        let finalHeight = min(contentHeight + dragIndicatorHeight, maxHeight)
        
        // 主 StackView - 初始位置在屏幕底部外
        mainStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            mainStackTopConstraint = make.top.equalTo(view.snp.bottom).constraint
            mainStackHeightConstraint = make.height.equalTo(finalHeight).constraint
        }
    }
    
    private func setupGestures() {
        // 背景点击手势
        let backgroundTap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(backgroundTap)
        
        // 拖拽手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        mainStackView.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Animation Methods
    
    private func animatePresentation() {
        // 更新约束以显示弹窗
        mainStackTopConstraint.update(offset: -mainStackView.bounds.height)
        
        UIView.animate(withDuration: animationDuration, delay: 0, options: [.curveEaseOut]) {
            self.backgroundView.alpha = 1
            self.view.layoutIfNeeded()
        }
    }
    
    private func animateDismissal(completion: (() -> Void)? = nil) {
        // 更新约束以隐藏弹窗
        mainStackTopConstraint.update(offset: 0)
        
        UIView.animate(withDuration: animationDuration, delay: 0, options: [.curveEaseIn]) {
            self.backgroundView.alpha = 0
            self.view.layoutIfNeeded()
        } completion: { _ in
            completion?()
        }
    }
    
    // MARK: - Action Methods
    
    @objc private func backgroundTapped() {
        dismiss(animated: true)
    }
    
    // MARK: - Public Methods for Navigation
    
    /// 推入新的内容视图（类似 UINavigationController 的 push）
    /// - Parameter newContentView: 新的内容视图
    func pushContentView(_ newContentView: BottomSheetContentProtocol) {
        // 将新视图添加到栈中
        contentViewStack.append(newContentView)
        
        // 准备新的内容视图
        let newContentUIView = newContentView.contentView
        newContentUIView.alpha = 0
        newContentUIView.transform = CGAffineTransform(translationX: view.bounds.width, y: 0)
        
        // 将新视图添加到容器中
        containerView.addSubview(newContentUIView)
        newContentUIView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 强制布局以获取正确的高度
        newContentUIView.setNeedsLayout()
        newContentUIView.layoutIfNeeded()
        view.layoutIfNeeded()
        
        // 计算新的高度
        let dragIndicatorHeight: CGFloat = showDragIndicator ? 28 : 0
        let newHeight = min(newContentView.preferredContentHeight + dragIndicatorHeight, view.bounds.height * maxHeightRatio)
        
        // 更新 mainStackView 的约束以适应新的高度
        mainStackHeightConstraint.update(offset: newHeight)
        
        // 同时更新 top 约束以保持底部对齐
        mainStackTopConstraint.update(offset: -(newHeight))
        
        // 执行推入动画
        UIView.animate(withDuration: animationDuration, delay: 0, options: [.curveEaseInOut]) {
            // 旧视图向左滑出
            self.contentView.contentView.alpha = 0
            self.contentView.contentView.transform = CGAffineTransform(translationX: -self.view.bounds.width, y: 0)
            
            // 新视图滑入
            newContentUIView.alpha = 1
            newContentUIView.transform = .identity
            
            // 更新容器高度和位置
            self.view.layoutIfNeeded()
        } completion: { _ in
            // 移除旧视图
            self.contentView.contentView.removeFromSuperview()
            
            // 更新当前内容视图
            self.contentView = newContentView
            
            // 设置新内容视图的回调
            self.contentView.onDismiss = { [weak self] in
                self?.dismiss(animated: true)
            }
        }
    }
    
    /// 弹出当前内容视图（类似 UINavigationController 的 pop）
    func popContentView() {
        guard contentViewStack.count > 1 else { return }
        
        // 移除当前视图
        contentViewStack.removeLast()
        
        // 获取上一个视图
        let previousContentView = contentViewStack.last!
        
        // 准备上一个内容视图
        let previousContentUIView = previousContentView.contentView
        previousContentUIView.alpha = 0
        previousContentUIView.transform = CGAffineTransform(translationX: -view.bounds.width, y: 0)
        
        // 将上一个视图添加到容器中
        containerView.addSubview(previousContentUIView)
        previousContentUIView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 强制布局以获取正确的高度
        previousContentUIView.setNeedsLayout()
        previousContentUIView.layoutIfNeeded()
        view.layoutIfNeeded()
        
        // 计算上一个视图的高度
        let dragIndicatorHeight: CGFloat = showDragIndicator ? 28 : 0
        let previousHeight = min(previousContentView.preferredContentHeight + dragIndicatorHeight, view.bounds.height * maxHeightRatio)
        
        // 更新 mainStackView 的约束以适应新的高度
        mainStackHeightConstraint.update(offset: previousHeight)
        
        // 同时更新 top 约束以保持底部对齐
        mainStackTopConstraint.update(offset: -(previousHeight))
        
        // 执行弹出动画
        UIView.animate(withDuration: animationDuration, delay: 0, options: [.curveEaseInOut]) {
            // 当前视图向右滑出
            self.contentView.contentView.alpha = 0
            self.contentView.contentView.transform = CGAffineTransform(translationX: self.view.bounds.width, y: 0)
            
            // 上一个视图滑入
            previousContentUIView.alpha = 1
            previousContentUIView.transform = .identity
            
            // 更新容器高度和位置
            self.view.layoutIfNeeded()
        } completion: { _ in
            // 移除当前视图
            self.contentView.contentView.removeFromSuperview()
            
            // 更新当前内容视图
            self.contentView = previousContentView
            
            // 设置内容视图的回调
            self.contentView.onDismiss = { [weak self] in
                self?.dismiss(animated: true)
            }
        }
    }
    
    /// 更新拖拽指示器的显示状态
    /// - Parameter show: 是否显示拖拽指示器
    func updateDragIndicatorVisibility(_ show: Bool) {
        guard show != showDragIndicator else { return }
        
        UIView.animate(withDuration: 0.25) {
            if show {
                if !self.mainStackView.arrangedSubviews.contains(self.dragIndicatorContainerView) {
                    self.mainStackView.insertArrangedSubview(self.dragIndicatorContainerView, at: 0)
                }
                self.dragIndicatorContainerView.alpha = 1
            } else {
                self.dragIndicatorContainerView.alpha = 0
            }
            
            // 重新计算高度
            let contentHeight = self.contentView.preferredContentHeight
            let dragIndicatorHeight: CGFloat = show ? 28 : 0
            let maxHeight = self.view.bounds.height * self.maxHeightRatio
            let finalHeight = min(contentHeight + dragIndicatorHeight, maxHeight)
            
            self.mainStackHeightConstraint.update(offset: finalHeight)
            self.view.layoutIfNeeded()
        } completion: { _ in
            if !show && self.mainStackView.arrangedSubviews.contains(self.dragIndicatorContainerView) {
                self.mainStackView.removeArrangedSubview(self.dragIndicatorContainerView)
                self.dragIndicatorContainerView.removeFromSuperview()
            }
        }
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        
        switch gesture.state {
        case .changed:
            // 只允许向下拖拽
            if translation.y > 0 {
                mainStackView.transform = CGAffineTransform(translationX: 0, y: translation.y)
                // 根据拖拽距离调整背景透明度
                let progress = min(translation.y / 200, 1.0)
                backgroundView.alpha = 1.0 - progress * 0.5
            }
            
        case .ended, .cancelled:
            let shouldDismiss = translation.y > 100 || velocity.y > 1000
            
            if shouldDismiss {
                dismiss(animated: true)
            } else {
                // 恢复到原始位置
                UIView.animate(withDuration: 0.25, delay: 0, options: [.curveEaseOut]) {
                    self.mainStackView.transform = .identity
                    self.backgroundView.alpha = 1.0
                }
            }
            
        default:
            break
        }
    }
}

// MARK: - BottomSheetContentProtocol

/**
 底部半弹窗内容协议
 
 实现此协议的视图可以作为 BottomSheetViewController 的内容视图
 */
protocol BottomSheetContentProtocol {
    /// 内容视图 - 实际显示的 UIView
    var contentView: UIView { get }
    
    /// 偏好的内容高度
    var preferredContentHeight: CGFloat { get }
    
    /// 关闭回调 - 当需要关闭弹窗时调用
    var onDismiss: (() -> Void)? { get set }
} 
