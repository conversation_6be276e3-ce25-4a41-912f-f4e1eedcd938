//
//  PermissionTypes.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 07/07/2025.
//

import Foundation

// MARK: - Permission Types

/// 统一的权限状态，方便 ViewModel 处理
public enum PermissionStatus {
    case granted
    case denied
    case notDetermined // 尚未请求
}

/// 定义一个权限类型枚举，方便管理
public enum PermissionType: String, CaseIterable {
    case camera
    case photoLibrary
    case localNetwork
}

// MARK: - Permission Result

/// 权限请求结果
enum PermissionResult {
    case granted
    case denied
    case restricted
    case notDetermined
    case error(PermissionError)
}

// MARK: - Permission Error

/// 权限相关错误
enum PermissionError: Error, LocalizedError {
    case systemError(Error)
    case unsupportedPermission(PermissionType)
    case userCancelled
    case settingsUnavailable
    
    var errorDescription: String? {
        switch self {
        case .systemError(let error):
            return "系统错误: \(error.localizedDescription)"
        case .unsupportedPermission(let type):
            return "不支持的权限类型: \(type.rawValue)"
        case .userCancelled:
            return "用户取消了权限请求"
        case .settingsUnavailable:
            return "无法打开系统设置"
        }
    }
}

// MARK: - Permission Configuration

/// 权限配置
struct PermissionConfiguration {
    let type: PermissionType
    let title: String
    let description: String
    let iconName: String
    let isRequired: Bool
    
    static let defaultConfigurations: [PermissionType: PermissionConfiguration] = [
        .camera: PermissionConfiguration(
            type: .camera,
            title: "相机",
            description: "用于扫描二维码和连接两台手机进行数据传输。",
            iconName: "camera.fill",
            isRequired: true
        ),
        .photoLibrary: PermissionConfiguration(
            type: .photoLibrary,
            title: "照片",
            description: "用于检索照片或将照片写入当前手机。",
            iconName: "photo.on.rectangle",
            isRequired: true
        ),
        .localNetwork: PermissionConfiguration(
            type: .localNetwork,
            title: "本地网络",
            description: "用于发现周围设备以建立设备连接。",
            iconName: "wifi",
            isRequired: true
        )
    ]
}
