//
//  PermissionsManager.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 06/07/2025.
//

import Foundation
import AVFoundation // 用于相机
import Photos       // 用于照片

#if canImport(UIKit)
import UIKit
#endif

// 统一的权限状态，方便 ViewModel 处理
enum PermissionStatus {
    case granted
    case denied
    case notDetermined // 尚未请求
}

// 定义一个权限类型枚举，方便管理
enum PermissionType: String, CaseIterable {
    case camera
    case photoLibrary
    case localNetwork
}

class PermissionsManager {
    
    // MARK: - Private Properties
    
    // 本地网络权限检查器 - 延迟初始化
    private lazy var localNetworkChecker: LocalNetworkPermissionChecker = {
        return LocalNetworkPermissionChecker()
    }()
    
    // UserDefaults Keys
    private enum UserDefaultsKeys {
        static let hasTriggeredLocalNetworkPermission = "ChatsGo_HasTriggeredLocalNetworkPermission"
    }

    // MARK: - Public API

    // 检查所有必要的权限，并返回一个字典，方便UI逐项更新
    func checkAllPermissions() async -> [PermissionType: PermissionStatus] {
        var statuses: [PermissionType: PermissionStatus] = [:]
        statuses[.camera] = checkCameraPermission()
        statuses[.photoLibrary] = checkPhotoLibraryPermission()
        statuses[.localNetwork] = await checkLocalNetworkPermission()
        return statuses
    }
    
    // 同步版本的权限检查，用于兼容现有的同步调用
    func checkAllPermissions() -> [PermissionType: PermissionStatus] {
        var statuses: [PermissionType: PermissionStatus] = [:]
        statuses[.camera] = checkCameraPermission()
        statuses[.photoLibrary] = checkPhotoLibraryPermission()
        
        // 本地网络权限的同步检查逻辑
        let hasTriggered = UserDefaults.standard.bool(forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
        if hasTriggered {
            // 如果已经触发过权限弹窗，需要异步检查实际状态
            // 这里返回一个默认状态，实际状态由异步方法更新
            statuses[.localNetwork] = .notDetermined
            
            // 异步检查并更新状态
            Task {
                let actualStatus = await checkLocalNetworkPermission()
                // 这里可以通过通知或回调通知UI更新
                NotificationCenter.default.post(
                    name: .localNetworkPermissionUpdated,
                    object: nil,
                    userInfo: ["status": actualStatus]
                )
            }
        } else {
            // 如果未触发过权限弹窗，返回未确定状态
            statuses[.localNetwork] = .notDetermined
        }
        
        return statuses
    }

    // 请求相机权限
    func requestCameraAccess(completion: @escaping (Bool) -> Void) {
        AVCaptureDevice.requestAccess(for: .video) { granted in
            DispatchQueue.main.async {
                completion(granted)
            }
        }
    }

    // 请求照片权限
    func requestPhotoLibraryAccess(completion: @escaping (Bool) -> Void) {
        // .readWrite 级别，因为数据迁移可能需要读取和写入
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
            DispatchQueue.main.async {
                completion(status == .authorized || status == .limited)
            }
        }
    }

    // 请求本地网络权限 - 委托给 LocalNetworkPermissionChecker
    func requestLocalNetworkAccess(completion: @escaping (Bool) -> Void) {
        Task {
            // 标记已经触发过本地网络权限弹窗
            UserDefaults.standard.set(true, forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
            
            // 使用 LocalNetworkPermissionChecker 处理权限请求
            let hasPermission = await localNetworkChecker.checkAndRequestPermission(showAlert: false)
            
            DispatchQueue.main.async {
                completion(hasPermission)
            }
        }
    }
    
    // 触发本地网络权限弹窗（仅用于首次触发）
    func triggerLocalNetworkScan() {
        Task {
            // 标记已经触发过本地网络权限弹窗
            UserDefaults.standard.set(true, forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
            
            // 委托给 LocalNetworkPermissionChecker 触发权限弹窗
            _ = await localNetworkChecker.triggerPermissionDialog()
        }
    }

    // 引导用户去系统设置
    func openSettings() {
        #if canImport(UIKit)
        guard let url = URL(string: UIApplication.openSettingsURLString),
              UIApplication.shared.canOpenURL(url) else {
            return
        }
        UIApplication.shared.open(url)
        #endif
    }

    // MARK: - Private Permission Checkers

    private func checkCameraPermission() -> PermissionStatus {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: return .granted
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }

    private func checkPhotoLibraryPermission() -> PermissionStatus {
        switch PHPhotoLibrary.authorizationStatus(for: .readWrite) {
        case .authorized, .limited: return .granted // limited 也视为已授权
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }
    
    private func checkLocalNetworkPermission() async -> PermissionStatus {
        let hasTriggered = UserDefaults.standard.bool(forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)
        
        if !hasTriggered {
            // 如果从未触发过权限弹窗，返回未确定状态
            return .notDetermined
        } else {
            // 如果已经触发过权限弹窗，使用 LocalNetworkPermissionChecker 检查实际的权限状态
            let hasPermission = await localNetworkChecker.checkPermission()
            return hasPermission ? .granted : .denied
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let localNetworkPermissionUpdated = Notification.Name("LocalNetworkPermissionUpdated")
}

// MARK: - 使用示例和集成指南

/*
使用说明：

1. 基本权限检查（异步版本，推荐）：
```swift
Task {
    let statuses = await permissionsManager.checkAllPermissions()
    // 处理权限状态
}
```

2. 兼容现有同步调用：
```swift
let statuses = permissionsManager.checkAllPermissions()
// 监听本地网络权限更新
NotificationCenter.default.addObserver(
    forName: .localNetworkPermissionUpdated,
    object: nil,
    queue: .main
) { notification in
    if let status = notification.userInfo?["status"] as? PermissionStatus {
        // 更新UI
    }
}
```

3. 请求特定权限：
```swift
// 相机权限
permissionsManager.requestCameraAccess { granted in
    // 处理结果
}

// 照片权限
permissionsManager.requestPhotoLibraryAccess { granted in
    // 处理结果
}

// 本地网络权限 - 委托给 LocalNetworkPermissionChecker
permissionsManager.requestLocalNetworkAccess { granted in
    // 处理结果
}
```

4. 首次触发本地网络权限（仅触发弹窗，不等待结果）：
```swift
permissionsManager.triggerLocalNetworkScan()
```

架构原则：
- PermissionsManager 作为权限管理协调器，负责统一的权限状态管理和 UserDefaults 跟踪
- LocalNetworkPermissionChecker 专门负责本地网络权限的所有技术实现细节
- 保持单一职责，避免代码重复
- 通过委托模式实现松耦合
*/
