//
//  PermissionsManager.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 06/07/2025.
//

import Foundation
import AVFoundation // 用于相机
import Photos       // 用于照片

#if canImport(UIKit)
import UIKit
#endif

// 类型定义已移至 PermissionTypes.swift

// MARK: - Permission Service Implementation

/// 权限服务的具体实现
class PermissionService: PermissionServiceProtocol {

    // MARK: - Singleton
    static let shared = PermissionService()

    // MARK: - Private Properties

    // 本地网络权限检查器 - 延迟初始化
    private lazy var localNetworkChecker: LocalNetworkPermissionChecker = {
        return LocalNetworkPermissionChecker()
    }()

    // UserDefaults Keys
    private enum UserDefaultsKeys {
        static let hasTriggeredLocalNetworkPermission = "ChatsGo_HasTriggeredLocalNetworkPermission"
    }

    // 私有初始化器，确保单例模式
    private init() {}

    // MARK: - PermissionServiceProtocol Implementation

    func checkAllPermissions() async -> [PermissionType: PermissionStatus] {
        var statuses: [PermissionType: PermissionStatus] = [:]

        // 并发检查所有权限以提高性能
        async let cameraStatus = checkPermission(for: .camera)
        async let photoStatus = checkPermission(for: .photoLibrary)
        async let networkStatus = checkPermission(for: .localNetwork)

        statuses[.camera] = await cameraStatus
        statuses[.photoLibrary] = await photoStatus
        statuses[.localNetwork] = await networkStatus

        return statuses
    }

    func checkPermission(for type: PermissionType) async -> PermissionStatus {
        switch type {
        case .camera:
            return checkCameraPermission()
        case .photoLibrary:
            return checkPhotoLibraryPermission()
        case .localNetwork:
            return await checkLocalNetworkPermission()
        }
    }

    func requestPermission(for type: PermissionType) async -> PermissionResult {
        switch type {
        case .camera:
            return await requestCameraPermission()
        case .photoLibrary:
            return await requestPhotoLibraryPermission()
        case .localNetwork:
            return await requestLocalNetworkPermission()
        }
    }

    func areAllPermissionsGranted(for requiredPermissions: [PermissionType]) async -> Bool {
        let statuses = await checkAllPermissions()
        return requiredPermissions.allSatisfy { type in
            statuses[type] == .granted
        }
    }

    func openSettings() {
        #if canImport(UIKit)
        guard let url = URL(string: UIApplication.openSettingsURLString),
              UIApplication.shared.canOpenURL(url) else {
            return
        }
        UIApplication.shared.open(url)
        #endif
    }

    // MARK: - Private Permission Request Methods

    private func requestCameraPermission() async -> PermissionResult {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                let result: PermissionResult = granted ? .granted : .denied
                continuation.resume(returning: result)
            }
        }
    }

    private func requestPhotoLibraryPermission() async -> PermissionResult {
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                let result: PermissionResult
                switch status {
                case .authorized, .limited:
                    result = .granted
                case .denied:
                    result = .denied
                case .restricted:
                    result = .restricted
                case .notDetermined:
                    result = .notDetermined
                @unknown default:
                    result = .notDetermined
                }
                continuation.resume(returning: result)
            }
        }
    }

    private func requestLocalNetworkPermission() async -> PermissionResult {
        do {
            // 标记已经触发过本地网络权限弹窗
            UserDefaults.standard.set(true, forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)

            // 使用 LocalNetworkPermissionChecker 处理权限请求
            let hasPermission = await localNetworkChecker.checkPermission()
            return hasPermission ? .granted : .denied
        } catch {
            return .error(.systemError(error))
        }
    }

    // MARK: - Private Permission Checkers

    private func checkCameraPermission() -> PermissionStatus {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: return .granted
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }

    private func checkPhotoLibraryPermission() -> PermissionStatus {
        switch PHPhotoLibrary.authorizationStatus(for: .readWrite) {
        case .authorized, .limited: return .granted // limited 也视为已授权
        case .denied, .restricted: return .denied
        case .notDetermined: return .notDetermined
        @unknown default: return .notDetermined
        }
    }

    private func checkLocalNetworkPermission() async -> PermissionStatus {
        let hasTriggered = UserDefaults.standard.bool(forKey: UserDefaultsKeys.hasTriggeredLocalNetworkPermission)

        if !hasTriggered {
            // 如果从未触发过权限弹窗，返回未确定状态
            return .notDetermined
        } else {
            // 如果已经触发过权限弹窗，使用 LocalNetworkPermissionChecker 检查实际的权限状态
            let hasPermission = await localNetworkChecker.checkPermission()
            return hasPermission ? .granted : .denied
        }
    }
}

// MARK: - Legacy Support (Backward Compatibility)

/// 保持向后兼容的旧版PermissionsManager类
/// 建议新代码使用PermissionService.shared
class PermissionsManager {

    // 委托给新的PermissionService
    private let permissionService: PermissionServiceProtocol = PermissionService.shared

    // MARK: - Legacy Methods

    /// 异步检查所有权限（推荐使用）
    func checkAllPermissions() async -> [PermissionType: PermissionStatus] {
        return await permissionService.checkAllPermissions()
    }

    /// 同步版本的权限检查（已废弃，建议使用异步版本）
    @available(*, deprecated, message: "使用异步版本 checkAllPermissions() async")
    func checkAllPermissions() -> [PermissionType: PermissionStatus] {
        // 为了兼容性，返回当前可以同步获取的状态
        var statuses: [PermissionType: PermissionStatus] = [:]

        // 相机和照片权限可以同步获取
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: statuses[.camera] = .granted
        case .denied, .restricted: statuses[.camera] = .denied
        case .notDetermined: statuses[.camera] = .notDetermined
        @unknown default: statuses[.camera] = .notDetermined
        }

        switch PHPhotoLibrary.authorizationStatus(for: .readWrite) {
        case .authorized, .limited: statuses[.photoLibrary] = .granted
        case .denied, .restricted: statuses[.photoLibrary] = .denied
        case .notDetermined: statuses[.photoLibrary] = .notDetermined
        @unknown default: statuses[.photoLibrary] = .notDetermined
        }

        // 本地网络权限需要异步检查，这里返回基于UserDefaults的估计状态
        let hasTriggered = UserDefaults.standard.bool(forKey: "ChatsGo_HasTriggeredLocalNetworkPermission")
        statuses[.localNetwork] = hasTriggered ? .notDetermined : .notDetermined

        // 异步更新本地网络权限状态
        Task {
            let networkStatus = await permissionService.checkPermission(for: .localNetwork)
            NotificationCenter.default.post(
                name: .localNetworkPermissionUpdated,
                object: nil,
                userInfo: ["status": networkStatus]
            )
        }

        return statuses
    }

    /// 打开系统设置
    func openSettings() {
        permissionService.openSettings()
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let localNetworkPermissionUpdated = Notification.Name("LocalNetworkPermissionUpdated")
}
