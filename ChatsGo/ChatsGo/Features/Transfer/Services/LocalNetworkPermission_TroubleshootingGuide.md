# 本地网络权限问题解决指南

## 🚨 **问题描述**

遇到的错误：`nw_browser_fail_on_dns_error_locked [B1] DNSServiceBrowse failed: NoAuth(-65555)`

这表示本地网络权限被拒绝，但没有弹出系统授权弹窗。

## ✅ **已实施的修复**

### 1. **Info.plist 配置更新**

添加了 `_airplay._tcp` 到 NSBonjourServices：

```xml
<key>NSBonjourServices</key>
<array>
    <string>_http._tcp.</string>
    <string>_airplay._tcp.</string>  <!-- 新添加 -->
    <string>_receiver._tcp</string>
    <string>_receiver._udp</string>
</array>
```

### 2. **多重权限检查策略**

现在 `requestPermission()` 会依次尝试：

1. **_airplay._tcp** (最容易触发权限弹窗)
2. **_http._tcp** (备用方案)
3. **UDP 连接** (最后的尝试)

### 3. **详细的错误日志**

添加了详细的日志输出，帮助诊断问题：

```
🔍 尝试触发本地网络权限弹窗...
❌ Bonjour browser failed for _airplay._tcp: Error...
   POSIX error code: 65555
   -> 本地网络权限被拒绝 (NoAuth)
```

## 🔧 **使用方法**

### 基本使用

```swift
let checker = LocalNetworkPermissionChecker()

// 请求权限（会尝试多种方法）
let granted = await checker.requestPermission()

if granted {
    print("✅ 权限获得")
} else {
    print("❌ 权限被拒绝")
}
```

### 处理权限被拒绝的情况

```swift
// 如果用户之前拒绝了权限，使用强制请求
let forceGranted = await checker.forceRequestPermission()
```

## 🐛 **调试步骤**

### 1. **检查控制台日志**

运行应用后，在 Xcode 控制台查看详细日志：

```
🔍 尝试触发本地网络权限弹窗...
✅ Bonjour browser ready for _airplay._tcp
```

或者：

```
❌ Bonjour browser failed for _airplay._tcp: Error...
⚠️ _airplay._tcp 失败，尝试 _http._tcp...
```

### 2. **检查设备设置**

在 iOS 设备上：
1. 设置 → 隐私与安全性 → 本地网络
2. 找到您的应用
3. 确认权限状态

### 3. **重置权限状态**

如果需要重新测试权限弹窗：

```swift
// 清除缓存
checker.clearCache()

// 或者完全重置应用权限
// 设置 → 通用 → 传输或还原iPhone → 还原 → 还原位置与隐私
```

## 🎯 **常见问题解决**

### Q1: 仍然没有弹出权限弹窗

**可能原因：**
- 用户之前已经拒绝过权限
- 应用在模拟器上运行（模拟器可能不支持）
- iOS 版本问题

**解决方案：**
```swift
// 使用强制请求方法
let result = await checker.forceRequestPermission()
```

### Q2: 权限检查总是返回 false

**可能原因：**
- 网络连接问题
- 防火墙阻止
- 设备限制

**解决方案：**
1. 检查设备网络连接
2. 尝试在真机上测试
3. 检查企业设备管理限制

### Q3: 错误代码含义

| 错误代码 | 含义 | 解决方案 |
|---------|------|---------|
| -65555 (NoAuth) | 权限被拒绝 | 引导用户到设置中开启 |
| -50 (ENETDOWN) | 网络不可用 | 检查网络连接 |
| -65563 (NoSuchName) | 服务不存在 | 正常，继续尝试其他方法 |

## 📱 **测试建议**

### 1. **在真机上测试**

本地网络权限在模拟器上可能不准确，建议在真机上测试。

### 2. **测试流程**

1. 全新安装应用
2. 首次调用权限检查
3. 观察是否弹出系统权限弹窗
4. 测试拒绝和允许两种情况

### 3. **日志监控**

在测试时，密切关注控制台输出，了解权限检查的详细过程。

## 🚀 **最佳实践**

### 1. **渐进式权限请求**

```swift
// 先检查缓存
let cached = await checker.checkPermission()
if !cached {
    // 再请求权限
    let granted = await checker.requestPermission()
}
```

### 2. **用户友好的错误处理**

```swift
let granted = await checker.requestPermission()
if !granted {
    // 显示友好的错误提示
    showLocalNetworkPermissionGuide()
}
```

### 3. **定期清理缓存**

```swift
// 在应用启动时清理过期缓存
checker.clearCache()
```

## 📞 **如果问题仍然存在**

如果按照以上步骤仍然无法解决问题，请提供：

1. 完整的控制台日志
2. iOS 版本和设备型号
3. 应用的具体使用场景
4. 权限弹窗的预期行为

这将帮助进一步诊断和解决问题。
