//
//  PermissionServiceProtocol.swift
//  ChatsGo
//
//  Created by <PERSON>o on 07/07/2025.
//

import Foundation

// MARK: - Permission Service Protocol

/// 权限服务协议，定义统一的权限管理接口
protocol PermissionServiceProtocol {
    
    /// 检查所有权限状态
    /// - Returns: 权限类型到状态的映射
    func checkAllPermissions() async -> [PermissionType: PermissionStatus]
    
    /// 检查特定权限状态
    /// - Parameter type: 权限类型
    /// - Returns: 权限状态
    func checkPermission(for type: PermissionType) async -> PermissionStatus
    
    /// 请求特定权限
    /// - Parameter type: 权限类型
    /// - Returns: 权限请求结果
    func requestPermission(for type: PermissionType) async -> PermissionResult
    
    /// 打开系统设置页面
    func openSettings()
    
    /// 检查是否所有必需权限都已授权
    /// - Parameter requiredPermissions: 必需的权限列表
    /// - Returns: 是否所有权限都已授权
    func areAllPermissionsGranted(for requiredPermissions: [PermissionType]) async -> Bool
}

// 类型定义已移至 PermissionTypes.swift

// MARK: - Permission Service Factory

/// 权限服务工厂
class PermissionServiceFactory {
    
    /// 创建权限服务实例
    /// - Returns: 权限服务实例
    static func createPermissionService() -> PermissionServiceProtocol {
        // 这里会在PermissionService实现后返回实例
        fatalError("PermissionService implementation needed")
    }
    
    /// 创建用于测试的模拟权限服务
    /// - Parameter mockStatuses: 模拟的权限状态
    /// - Returns: 模拟权限服务实例
    static func createMockPermissionService(
        mockStatuses: [PermissionType: PermissionStatus] = [:]
    ) -> PermissionServiceProtocol {
        return MockPermissionService(mockStatuses: mockStatuses)
    }
}

// MARK: - Mock Permission Service (for testing)

/// 模拟权限服务，用于单元测试
class MockPermissionService: PermissionServiceProtocol {
    
    private var mockStatuses: [PermissionType: PermissionStatus]
    private var shouldFailRequests: Bool
    
    init(mockStatuses: [PermissionType: PermissionStatus] = [:], shouldFailRequests: Bool = false) {
        self.mockStatuses = mockStatuses
        self.shouldFailRequests = shouldFailRequests
    }
    
    func checkAllPermissions() async -> [PermissionType: PermissionStatus] {
        return mockStatuses
    }
    
    func checkPermission(for type: PermissionType) async -> PermissionStatus {
        return mockStatuses[type] ?? .notDetermined
    }
    
    func requestPermission(for type: PermissionType) async -> PermissionResult {
        if shouldFailRequests {
            return .error(.userCancelled)
        }
        
        // 模拟权限请求成功
        mockStatuses[type] = .granted
        return .granted
    }
    
    func openSettings() {
        // 模拟打开设置（在测试中不执行实际操作）
    }
    
    func areAllPermissionsGranted(for requiredPermissions: [PermissionType]) async -> Bool {
        return requiredPermissions.allSatisfy { type in
            mockStatuses[type] == .granted
        }
    }
    
    // MARK: - Test Helpers
    
    func setMockStatus(_ status: PermissionStatus, for type: PermissionType) {
        mockStatuses[type] = status
    }
    
    func setShouldFailRequests(_ shouldFail: Bool) {
        shouldFailRequests = shouldFail
    }
}
