//
//  LocalNetworkPermissionChecker.swift
//  ChatsGo
//
//  Created by Subo on 07/07/2025.
//

import Network
import Foundation

#if canImport(UIKit)
import UIKit
#endif

class LocalNetworkPermissionChecker {

    // MARK: - 私有 Actor 用于安全管理并发状态
    /// 这个 Actor 封装了 continuation 和一个状态标记，以确保在并发环境中安全地、仅执行一次 resume 操作。
    private actor ContinuationActor<T> {
        private var continuation: CheckedContinuation<T, Never>?
        private var hasResumed = false

        init(continuation: CheckedContinuation<T, Never>) {
            self.continuation = continuation
        }

        /// 尝试恢复 continuation。如果已经恢复过，则不执行任何操作。
        /// - Parameter value: 要返回给 continuation 的值。
        func resume(returning value: T) {
            if !hasResumed {
                hasResumed = true
                continuation?.resume(returning: value)
                continuation = nil // 释放引用
            }
        }
    }


    /// 检查本地网络权限
    /// - Parameter timeout: 超时时间，默认 5 秒
    /// - Returns: 是否有本地网络权限
    func checkPermission(timeout: TimeInterval = 5.0) async -> <PERSON><PERSON> {
        return await withCheckedContinuation { continuation in
            // 使用 Actor 来管理状态，避免数据竞争
            let actor = ContinuationActor(continuation: continuation)

            // 创建 Bonjour 浏览器
            let browser = NWBrowser(for: .bonjour(type: "_http._tcp", domain: nil), using: .tcp)

            // 状态更新处理
            browser.stateUpdateHandler = { @Sendable state in
                switch state {
                case .ready:
                    // 浏览器准备就绪，说明有权限
                    Task { await actor.resume(returning: true) }
                    browser.cancel()

                case .failed(let error):
                    // 浏览器失败，检查错误类型
                    if case .posix(let posixError) = error, posixError.rawValue == 50 { // ENETDOWN - 网络权限被拒绝
                        Task { await actor.resume(returning: false) }
                    } else {
                        Task { await actor.resume(returning: false) }
                    }
                    browser.cancel()

                case .waiting:
                    // 等待状态，继续等待直到超时
                    break

                default:
                    break
                }
            }

            // 浏览结果变化处理
            browser.browseResultsChangedHandler = { @Sendable results, changes in
                // 如果能够接收到结果，说明有权限
                if !results.isEmpty {
                    Task { await actor.resume(returning: true) }
                    browser.cancel()
                }
            }

            // 启动浏览器
            browser.start(queue: .main)

            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + timeout) { @Sendable in
                Task { await actor.resume(returning: false) }
                browser.cancel()
            }
        }
    }

    /// 触发本地网络权限弹窗（如果是首次使用）
    /// - Parameter timeout: 超时时间，默认 3 秒
    /// - Returns: 是否成功触发（不代表用户授权结果）
    func triggerPermissionDialog(timeout: TimeInterval = 3.0) async -> Bool {
        return await withCheckedContinuation { continuation in
            let actor = ContinuationActor(continuation: continuation)

            // 创建一个用于触发权限弹窗的 Bonjour 浏览器
            let browser = NWBrowser(for: .bonjour(type: "_airplay._tcp", domain: nil), using: .tcp)

            browser.stateUpdateHandler = { @Sendable state in
                switch state {
                case .ready, .failed:
                    // 无论成功还是失败，都说明触发了权限检查（弹窗已显示或已处理过）
                    Task { await actor.resume(returning: true) }
                    browser.cancel()

                default:
                    break
                }
            }

            browser.start(queue: .main)

            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + timeout) { @Sendable in
                Task { await actor.resume(returning: false) }
                browser.cancel()
            }
        }
    }

    /// 检查并请求本地网络权限
    /// - Parameter showAlert: 是否在权限被拒绝时显示提示
    /// - Returns: 是否获得权限
    func checkAndRequestPermission(showAlert: Bool = true) async -> Bool {
        // 首先尝试触发权限弹窗。这一步对于确保在检查前弹出对话框很重要。
        _ = await triggerPermissionDialog()

        // 等待一小段时间让用户处理权限弹窗
        // 在实际调用中，系统弹窗会阻塞App，但给一点延时可以处理一些边缘情况
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        // 检查权限状态
        let hasPermission = await checkPermission()

        if !hasPermission && showAlert {
            await showPermissionAlert()
        }

        return hasPermission
    }

    /// 显示权限提示弹窗
    @MainActor
    private func showPermissionAlert() {
        #if canImport(UIKit)
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first(where: { $0.isKeyWindow }),
              let rootViewController = window.rootViewController else {
            return
        }

        let alert = UIAlertController(
            title: "需要本地网络权限",
            message: "此功能需要访问本地网络，请在设置中开启本地网络权限。",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        // 确保在最顶层的 ViewController 上弹出
        var topController = rootViewController
        while let presentedViewController = topController.presentedViewController {
            topController = presentedViewController
        }
        topController.present(alert, animated: true)
        #endif
    }
}

// MARK: - 使用示例

extension LocalNetworkPermissionChecker {

    /// 演示如何使用异步权限检查
    func demonstrateUsage() {
        Task {
            print("开始检查本地网络权限...")

            // 方法 1: 简单检查权限
            let hasPermission = await checkPermission()
            print("权限检查结果: \(hasPermission)")

            // 方法 2: 检查并请求权限
            let granted = await checkAndRequestPermission()
            if granted {
                print("✅ 本地网络权限已授予，可以继续执行网络操作")
                await performLocalNetworkOperations()
            } else {
                print("❌ 本地网络权限被拒绝")
            }
        }
    }

    /// 执行需要本地网络权限的操作
    private func performLocalNetworkOperations() async {
        print("正在执行本地网络操作...")
        // 这里可以添加实际的网络操作代码

        // 示例：扫描本地服务
        await scanLocalServices()
    }

    /// 扫描本地服务的示例
    private func scanLocalServices() async {
        return await withCheckedContinuation { continuation in
            let browser = NWBrowser(for: .bonjour(type: "_http._tcp", domain: nil), using: .tcp)

            browser.browseResultsChangedHandler = { @Sendable results, changes in
                print("发现 \(results.count) 个本地服务")
                for result in results {
                    print("- \(result.endpoint)")
                }

                // 扫描完成后停止
                browser.cancel()
                continuation.resume()
            }

            browser.start(queue: .main)

            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) { @Sendable in
                browser.cancel()
                continuation.resume()
            }
        }
    }
}

// MARK: - 在 ViewController 中的使用示例

/*
class ViewController: UIViewController {
    private let permissionChecker = LocalNetworkPermissionChecker()

    override func viewDidLoad() {
        super.viewDidLoad()

        // 在需要的时候检查权限
        Task {
            await checkNetworkPermission()
        }
    }

    private func checkNetworkPermission() async {
        let hasPermission = await permissionChecker.checkAndRequestPermission()

        if hasPermission {
            // 权限已授予，可以执行网络操作
            print("可以执行本地网络操作")
        } else {
            // 权限被拒绝，显示替代方案或禁用相关功能
            print("本地网络权限被拒绝，某些功能可能无法使用")
        }
    }

    @IBAction func scanButtonTapped(_ sender: UIButton) {
        Task {
            let hasPermission = await permissionChecker.checkPermission()

            if hasPermission {
                // 执行扫描操作
                await performScan()
            } else {
                // 请求权限
                let granted = await permissionChecker.checkAndRequestPermission()
                if granted {
                    await performScan()
                }
            }
        }
    }

    private func performScan() async {
        // 实际的扫描逻辑
        print("开始扫描本地网络...")
    }
}
*/
