//
//  LocalNetworkPermissionChecker.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 07/07/2025.
//

import Network
import Foundation

#if canImport(UIKit)
import UIKit
#endif

class LocalNetworkPermissionChecker {

    // MARK: - Properties

    /// 权限状态缓存
    private var cachedPermissionStatus: Bool?

    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 30.0

    /// 上次检查时间
    private var lastCheckTime: Date?

    /// 是否正在检查权限（防止并发检查）
    private var isChecking = false

    // MARK: - Constants

    /// 用于权限检查的服务类型 - _airplay._tcp 更容易触发权限弹窗
    private static let permissionCheckServiceType = "_airplay._tcp"

    /// 用于实际服务发现的服务类型
    private static let serviceDiscoveryType = "_http._tcp"

    // MARK: - 私有 Actor 用于安全管理并发状态
    /// 这个 Actor 封装了 continuation 和一个状态标记，以确保在并发环境中安全地、仅执行一次 resume 操作。
    private actor ContinuationActor<T> {
        private var continuation: CheckedContinuation<T, Never>?
        private var hasResumed = false

        init(continuation: CheckedContinuation<T, Never>) {
            self.continuation = continuation
        }

        /// 尝试恢复 continuation。如果已经恢复过，则不执行任何操作。
        /// - Parameter value: 要返回给 continuation 的值。
        func resume(returning value: T) {
            if !hasResumed {
                hasResumed = true
                continuation?.resume(returning: value)
                continuation = nil // 释放引用
            }
        }
    }


    // MARK: - Public Methods

    /// 检查本地网络权限（带缓存）
    /// - Parameters:
    ///   - forceCheck: 是否强制检查，忽略缓存
    ///   - timeout: 超时时间，默认 5 秒
    /// - Returns: 是否有本地网络权限
    func checkPermission(forceCheck: Bool = false, timeout: TimeInterval = 5.0) async -> Bool {
        // 检查缓存是否有效
        if !forceCheck, let cachedStatus = getCachedPermissionStatus() {
            return cachedStatus
        }

        // 防止并发检查
        if isChecking {
            // 等待当前检查完成
            while isChecking {
                try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
            }
            // 返回缓存结果
            return cachedPermissionStatus ?? false
        }

        isChecking = true
        defer { isChecking = false }

        let result = await performPermissionCheck(serviceType: Self.permissionCheckServiceType, timeout: timeout)

        // 更新缓存
        updateCache(with: result)

        return result
    }

    /// 请求本地网络权限（如果需要的话会触发系统弹窗）
    /// - Parameter showAlert: 权限被拒绝时是否显示提示
    /// - Returns: 是否获得权限
    func requestPermission(showAlert: Bool = true) async -> Bool {
        // 使用 _airplay._tcp 强制触发权限检查（可能会显示系统弹窗）
        let hasPermission = await performPermissionCheck(serviceType: Self.permissionCheckServiceType, timeout: 5.0)

        // 更新缓存
        updateCache(with: hasPermission)

        if !hasPermission && showAlert {
            await showPermissionAlert()
        }

        return hasPermission
    }

    /// 清除权限状态缓存
    func clearCache() {
        cachedPermissionStatus = nil
        lastCheckTime = nil
    }

    // MARK: - Private Methods

    /// 获取缓存的权限状态（如果有效）
    private func getCachedPermissionStatus() -> Bool? {
        guard let lastCheck = lastCheckTime,
              let cachedStatus = cachedPermissionStatus else {
            return nil
        }

        // 检查缓存是否过期
        let timeSinceLastCheck = Date().timeIntervalSince(lastCheck)
        if timeSinceLastCheck < cacheValidityDuration {
            return cachedStatus
        }

        return nil
    }

    /// 更新权限状态缓存
    private func updateCache(with status: Bool) {
        cachedPermissionStatus = status
        lastCheckTime = Date()
    }

    /// 执行实际的权限检查
    /// - Parameter serviceType: Bonjour服务类型，默认使用 _airplay._tcp 确保能触发权限检查
    /// - Parameter timeout: 超时时间
    /// - Returns: 是否有权限
    private func performPermissionCheck(serviceType: String, timeout: TimeInterval) async -> Bool {
        return await withCheckedContinuation { continuation in
            // 使用 Actor 来管理状态，避免数据竞争
            let actor = ContinuationActor(continuation: continuation)

            // 创建 Bonjour 浏览器 - 统一使用 _airplay._tcp 确保能触发权限检查
            let browser = NWBrowser(for: .bonjour(type: serviceType, domain: nil), using: .tcp)

            // 状态更新处理
            browser.stateUpdateHandler = { @Sendable state in
                switch state {
                case .ready:
                    // 浏览器准备就绪，说明有权限
                    Task { await actor.resume(returning: true) }
                    browser.cancel()

                case .failed(let error):
                    // 浏览器失败，检查错误类型
                    if case .posix(let posixError) = error, posixError.rawValue == 50 { // ENETDOWN - 网络权限被拒绝
                        Task { await actor.resume(returning: false) }
                    } else {
                        // 其他错误也可能表示权限问题
                        Task { await actor.resume(returning: false) }
                    }
                    browser.cancel()

                case .waiting:
                    // 等待状态，继续等待直到超时
                    break

                default:
                    break
                }
            }

            // 浏览结果变化处理
            browser.browseResultsChangedHandler = { @Sendable results, changes in
                // 如果能够接收到结果，说明有权限
                if !results.isEmpty {
                    Task { await actor.resume(returning: true) }
                    browser.cancel()
                }
            }

            // 启动浏览器
            browser.start(queue: .main)

            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + timeout) { @Sendable in
                Task { await actor.resume(returning: false) }
                browser.cancel()
            }
        }
    }

    /// 显示权限提示弹窗
    @MainActor
    private func showPermissionAlert() {
        #if canImport(UIKit)
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first(where: { $0.isKeyWindow }),
              let rootViewController = window.rootViewController else {
            return
        }

        let alert = UIAlertController(
            title: "需要本地网络权限",
            message: "此功能需要访问本地网络，请在设置中开启本地网络权限。",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        // 确保在最顶层的 ViewController 上弹出
        var topController = rootViewController
        while let presentedViewController = topController.presentedViewController {
            topController = presentedViewController
        }
        topController.present(alert, animated: true)
        #endif
    }
}

// MARK: - 使用示例

extension LocalNetworkPermissionChecker {

    /// 演示如何使用重构后的权限检查
    func demonstrateUsage() {
        Task {
            print("开始检查本地网络权限...")

            // 方法 1: 简单检查权限（使用缓存）
            let hasPermission = await checkPermission()
            print("权限检查结果: \(hasPermission)")

            // 方法 2: 强制检查权限（忽略缓存）
            let freshStatus = await checkPermission(forceCheck: true)
            print("强制检查结果: \(freshStatus)")

            // 方法 3: 请求权限（如果需要会触发系统弹窗）
            let granted = await requestPermission()
            if granted {
                print("✅ 本地网络权限已授予，可以继续执行网络操作")
                await performLocalNetworkOperations()
            } else {
                print("❌ 本地网络权限被拒绝")
            }
        }
    }

    /// 执行需要本地网络权限的操作
    private func performLocalNetworkOperations() async {
        print("正在执行本地网络操作...")
        // 这里可以添加实际的网络操作代码

        // 示例：扫描本地服务
        await scanLocalServices()
    }

    /// 扫描本地服务的示例
    private func scanLocalServices() async {
        return await withCheckedContinuation { continuation in
            // 注意：这里使用 _http._tcp 用于实际的服务发现，不是权限检查
            let browser = NWBrowser(for: .bonjour(type: "_http._tcp", domain: nil), using: .tcp)

            browser.browseResultsChangedHandler = { @Sendable results, changes in
                print("发现 \(results.count) 个本地服务")
                for result in results {
                    print("- \(result.endpoint)")
                }

                // 扫描完成后停止
                browser.cancel()
                continuation.resume()
            }

            browser.start(queue: .main)

            // 设置扫描超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) { @Sendable in
                browser.cancel()
                continuation.resume()
            }
        }
    }
}

// MARK: - 在 ViewController 中的使用示例

/*
class ViewController: UIViewController {
    private let permissionChecker = LocalNetworkPermissionChecker()

    override func viewDidLoad() {
        super.viewDidLoad()

        // 在需要的时候检查权限
        Task {
            await checkNetworkPermission()
        }
    }

    private func checkNetworkPermission() async {
        // 使用重构后的方法
        let hasPermission = await permissionChecker.requestPermission()

        if hasPermission {
            // 权限已授予，可以执行网络操作
            print("可以执行本地网络操作")
        } else {
            // 权限被拒绝，显示替代方案或禁用相关功能
            print("本地网络权限被拒绝，某些功能可能无法使用")
        }
    }

    @IBAction func scanButtonTapped(_ sender: UIButton) {
        Task {
            // 先检查缓存的权限状态
            let hasPermission = await permissionChecker.checkPermission()

            if hasPermission {
                // 执行扫描操作
                await performScan()
            } else {
                // 请求权限
                let granted = await permissionChecker.requestPermission()
                if granted {
                    await performScan()
                }
            }
        }
    }

    private func performScan() async {
        // 实际的扫描逻辑
        print("开始扫描本地网络...")
    }
}
*/
