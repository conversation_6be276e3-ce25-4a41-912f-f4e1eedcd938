//
//  HomePageController.swift
//  ChatsGo
//
//  Created by <PERSON><PERSON> on 02/07/2025.
//

import UIKit
import SnapKit

class HomePageController: UIViewController {

    // MARK: - Properties
    
    /// Transfer 协调器
    private lazy var transferCoordinator: TransferCoordinator = {
        return TransferCoordinator(presentingViewController: self)
    }()

    // MARK: - UI Components
    
    /// 背景图片视图 - 铺满整个屏幕
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "home_bg")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()
    
    /// 顶部容器视图 - 适配安全区域
    private lazy var topContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 左侧解锁功能按钮
    private lazy var unlockButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = UIColor.systemOrange
        button.layer.cornerRadius = 25
        button.setTitle("👑 Unlock all functions", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(unlockButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 右侧用户头像按钮
    private lazy var avatarButton: UIButton = {
        let button = ExpandableTouchAreaButton(type: .custom)
        button.layer.cornerRadius = 15
        button.setImage(UIImage(named: "avatar_icon"), for: .normal)
        button.addTarget(self, action: #selector(avatarButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 权限测试按钮 (临时)
    private lazy var permissionsTestButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("权限页面测试", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemBlue
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(permissionsTestButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 主标题标签
    private lazy var mainTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Phone Transfer"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = UIColor.label
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()
    
    /// 副标题标签
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Transfer your data from old phone to new phone."
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textColor = UIColor.label
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()
    
    /// 中间插图视图
    private lazy var illustrationImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "iphone.and.arrow.forward")
        imageView.tintColor = UIColor.systemBlue
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// Phone to Phone 功能卡片 - 使用 FunctionCardView
    private lazy var phoneToPhoneCard: FunctionCardView = {
        let cardItem = FunctionCardItem(
            title: "phone to phone",
            content: "Lorem ipsum dolor sit amet, consectetur",
            backgroundImageName: "phone_to_phone_bg", // 背景图片名称
            iconName: "phone_to_phone_icon"           // 图标名称
        )
        let cardView = FunctionCardView(cardItem: cardItem)
        
        // 设置点击回调
        cardView.onTapped = { [weak self] in
            self?.handlePhoneToPhoneCardTapped()
        }
        
        return cardView
    }()

    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.fd_prefersNavigationBarHidden = true

        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        view.backgroundColor = UIColor.systemBackground
        
        // 添加所有子视图
        view.addSubview(backgroundImageView)
        view.addSubview(topContainerView)
        view.addSubview(mainTitleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(illustrationImageView)
        illustrationImageView.isHidden = true // 这个版本不需要，下个版本的功能，所以先隐藏了
        view.addSubview(phoneToPhoneCard)
        view.addSubview(permissionsTestButton)
        
        // 添加顶部按钮到容器
        topContainerView.addSubview(unlockButton)
        // 这个版本不需要，下个版本的功能，所以先隐藏了
        unlockButton.isHidden = true
        topContainerView.addSubview(avatarButton)
    }
    
    private func setupConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        topContainerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(40)
        }
        
        unlockButton.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.trailing.equalTo(avatarButton.snp.leading).offset(-8)
        }
        
        avatarButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(30)
            make.centerY.equalToSuperview()
        }
        
        mainTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(topContainerView.snp.bottom).offset(28)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(mainTitleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalTo(mainTitleLabel)
        }
        
        phoneToPhoneCard.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalTo(permissionsTestButton.snp.top).offset(-16)
            make.height.equalTo(120)
        }
        
        permissionsTestButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-20)
            make.width.equalTo(120)
            make.height.equalTo(40)
        }
    }
    
    // MARK: - Action Methods
    
    @objc private func unlockButtonTapped() {
        print("解锁功能按钮被点击")
        // TODO: 实现解锁功能逻辑
    }
    
    @objc private func avatarButtonTapped() {
        print("用户头像按钮被点击")
        // TODO: 实现用户资料页面跳转
    }
    
    @objc private func permissionsTestButtonTapped() {
        print("权限页面测试按钮被点击")
        let permissionsVC = PermissionsViewController()
        navigationController?.pushViewController(permissionsVC, animated: true)
    }
    
    /// 处理 Phone to Phone 卡片点击
    private func handlePhoneToPhoneCardTapped() {
        print("Phone to Phone 卡片被点击")
        
        // 启动 Transfer 流程
        transferCoordinator.startTransferFlow()
    }
}
