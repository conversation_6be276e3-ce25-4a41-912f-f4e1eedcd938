//
//  SystemInfoViewController.swift
//  ChatsGo
//
//  Created by AI Assistant on 2024/12/19.
//

import UIKit

/// 系统信息展示页面
/// 演示 SystemInfoManager 的使用方法
class SystemInfoViewController: UIViewController {
    
    // MARK: - Properties
    
    private let systemInfoManager = SystemInfoManager.shared
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .systemBackground
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var refreshButton: UIBarButtonItem = {
        return UIBarButtonItem(
            barButtonSystemItem: .refresh,
            target: self,
            action: #selector(refreshButtonTapped)
        )
    }()
    
    private lazy var shareButton: UIBarButtonItem = {
        return UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(shareButtonTapped)
        )
    }()
    
    private lazy var reportTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = .secondarySystemBackground
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.separator.cgColor
        textView.font = .monospacedSystemFont(ofSize: 12, weight: .regular)
        textView.textColor = .label
        textView.isEditable = false
        textView.isSelectable = true
        textView.contentInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        return textView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadSystemInfo()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 页面显示时刷新信息
        loadSystemInfo()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "系统信息"
        
        // 设置导航栏
        navigationItem.rightBarButtonItems = [shareButton, refreshButton]
        
        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(reportTextView)
    }
    
    private func setupConstraints() {
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        reportTextView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // ScrollView 约束
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // ContentView 约束
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // ReportTextView 约束
            reportTextView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 16),
            reportTextView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            reportTextView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            reportTextView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -16),
            reportTextView.heightAnchor.constraint(greaterThanOrEqualToConstant: 600)
        ])
    }
    
    // MARK: - Private Methods
    
    private func loadSystemInfo() {
        // 开始加载动画
        let loadingIndicator = UIActivityIndicatorView(style: .medium)
        loadingIndicator.startAnimating()
        navigationItem.titleView = loadingIndicator
        
        // 异步加载系统信息，避免阻塞主线程
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            let report = self.systemInfoManager.generateSystemReport()
            
            DispatchQueue.main.async {
                // 停止加载动画
                loadingIndicator.stopAnimating()
                self.navigationItem.titleView = nil
                
                // 更新UI
                self.reportTextView.text = report
                
                // 记录日志
                LogManager.shared.info("系统信息已更新")
            }
        }
    }
    
    private func shareSystemReport() {
        let report = systemInfoManager.generateSystemReport()
        let activityViewController = UIActivityViewController(
            activityItems: [report],
            applicationActivities: nil
        )
        
        // iPad 支持
        if let popoverController = activityViewController.popoverPresentationController {
            popoverController.barButtonItem = shareButton
        }
        
        present(activityViewController, animated: true)
        
        LogManager.shared.info("用户分享了系统信息报告")
    }
    
    // MARK: - Actions
    
    @objc private func refreshButtonTapped() {
        loadSystemInfo()
        
        // 显示刷新反馈
        let feedbackGenerator = UIImpactFeedbackGenerator(style: .light)
        feedbackGenerator.impactOccurred()
        
        LogManager.shared.info("用户手动刷新了系统信息")
    }
    
    @objc private func shareButtonTapped() {
        shareSystemReport()
    }
}

// MARK: - 便捷扩展

extension SystemInfoViewController {
    
    /// 展示特定类型的系统信息
    /// - Parameter infoType: 信息类型
    func showSpecificInfo(_ infoType: SystemInfoType) {
        var infoText = ""
        
        switch infoType {
        case .battery:
            let batteryInfo = systemInfoManager.getBatteryInfo()
            infoText = """
            🔋 电池信息
            电量: \(batteryInfo.levelPercentage)
            状态: \(batteryInfo.stateDescription)
            低电量模式: \(batteryInfo.isLowPowerModeEnabled ? "已启用" : "未启用")
            """
            
        case .storage:
            let storageInfo = systemInfoManager.getStorageInfo()
            infoText = """
            💾 存储空间
            总容量: \(storageInfo.totalSpaceFormatted)
            可用空间: \(storageInfo.availableSpaceFormatted)
            已用空间: \(storageInfo.usedSpaceFormatted) (\(storageInfo.usagePercentageString))
            """
            
        case .memory:
            let memoryInfo = systemInfoManager.getMemoryInfo()
            infoText = """
            🧠 内存信息
            物理内存: \(memoryInfo.totalPhysicalMemoryFormatted)
            应用内存: \(memoryInfo.appMemoryUsageFormatted)
            可用内存: \(memoryInfo.availableMemoryFormatted)
            """
            
        case .device:
            let deviceInfo = systemInfoManager.getDeviceInfo()
            infoText = """
            📱 设备信息
            设备名称: \(deviceInfo.name)
            设备型号: \(deviceInfo.model)
            设备类型: \(deviceInfo.deviceTypeDescription)
            系统版本: \(deviceInfo.systemName) \(deviceInfo.systemVersion)
            """
            
        case .network:
            let networkInfo = systemInfoManager.getNetworkInfo()
            infoText = """
            🌐 网络状态
            连接状态: \(networkInfo.statusDescription)
            """
            
        case .system:
            let systemInfo = systemInfoManager.getSystemInfo()
            infoText = """
            📋 系统信息
            iOS版本: \(systemInfo.iOSVersion)
            应用版本: \(systemInfo.appFullVersion)
            运行时间: \(systemInfo.systemUptimeFormatted)
            语言: \(systemInfo.preferredLanguage)
            """
        }
        
        let alert = UIAlertController(title: infoType.title, message: infoText, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - 系统信息类型枚举

enum SystemInfoType: CaseIterable {
    case battery
    case storage
    case memory
    case device
    case network
    case system
    
    var title: String {
        switch self {
        case .battery: return "电池信息"
        case .storage: return "存储空间"
        case .memory: return "内存信息"
        case .device: return "设备信息"
        case .network: return "网络状态"
        case .system: return "系统信息"
        }
    }
} 