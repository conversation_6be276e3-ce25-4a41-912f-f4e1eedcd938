<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSBonjourServices</key>
	<array>
		<string>_http._tcp.</string>
		<string>_receiver._tcp</string>
		<string>_receiver._udp</string>
	</array>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Local network access is required to discover and connect to nearby devices.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Photo library access is required to save images and videos.</string>
	<key>NSCameraUsageDescription</key>
	<string>Camera access is required to scan QR codes for device connection.</string>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
</dict>
</plist>
