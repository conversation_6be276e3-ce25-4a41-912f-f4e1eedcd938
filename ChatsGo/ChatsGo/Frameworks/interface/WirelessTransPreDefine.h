#ifndef WirelessTransPreDefine_h
#define WirelessTransPreDefine_h
#include <iostream>

#define WIRELESSTRANSMANAGER_EXPORT

#ifdef WIRELESSTRANSMANAGER_EXPORT
#ifdef _WINDOWS
#define  WIRELESSTRANSMANAGER_API __declspec(dllexport)
#else
#define  WIRELESSTRANSMANAGER_API
#endif
#else
#define  WIRELESSTRANSMANAGER_API __declspec(dllimport)
#endif

typedef enum EN_DataType
{
    kDataType_Other = -1,
    kDataType_Photo = 0 ,
    kDataType_Video,
    kDataType_Audio,
    kDataType_Contact,
    kDataType_Calendar,
    kDataType_File,
}ENDataType, *pENDataType;

typedef enum EN_CallbackType
{
    kCallbackType_Start = 0,
    kCallbackType_Progress,
    kCallbackType_TransData,
    kCallbackType_End,
}ENCallbackType;

typedef enum EN_CallbackCode
{
    kCallbackCode_Success = 0,
    kCallbackCode_Fail,
    k<PERSON>allbackCode_Stop,
    
    kCallbackCode_ReceiveError = 100,
    k<PERSON>allbackCode_SaveError,
    kCallbackCode_JsonParseFail,
    kCallbackCode_NotEnoughDiskSpace,
    kCallbackCode_StatusError,
    
}ENCallbackCode;

/**
 * @brief Callback function for wireless data transmission events.
 *
 * @param enCallbackType Type of the callback event (e.g., receive start, receive complete, progress, error...).
 * @param code           EN_CallbackCode.
 * @param size           Size of the file in bytes.
 * @param data           User-defined data passed to the callback (can be nullptr).
 */
typedef void (*WirelessTransCallbackFunc)(ENCallbackType enCallbackType, int code, size_t size, void* data);

#endif /* WirelessTransPreDefine_h */
