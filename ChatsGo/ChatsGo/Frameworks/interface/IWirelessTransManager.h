#ifndef IWirelessTransManager_h
#define IWirelessTransManager_h

#include "WirelessTransPreDefine.h"

class WIRELESSTRANSMANAGER_API IWirelessTransManager
{
public:
    /**
     * @brief Connect to a socket server by IP and port (blocking).
     *
     * @param ip   Socket server IP address.
     * @param port Socket server port.
     * @return true if the connection succeeds, false otherwise.
     */
    virtual bool Connect(const char* ip, int port) = 0;
    
    /**
     * @brief Set the callback function for transmission events (blocking).
     *
     * @param callback Callback function.
     * @param data     User-defined data passed to the callback (can be nullptr).
     */
    virtual void SetCallback(WirelessTransCallbackFunc callback, void* data) = 0;
    
    /**
     * @brief Start receiving data (blocking).
     */
    virtual void ReceiveData() = 0;
    
    /**
     * @brief Stop receiving data (blocking).
     */
    virtual void Stop() = 0;

};

WIRELESSTRANSMANAGER_API IWirelessTransManager * CreateWirelessTransManager();

#endif /* IWirelessTransManager_h */
