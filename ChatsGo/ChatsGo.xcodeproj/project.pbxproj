// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		1D12DDFA75CF397413E0A374 /* Pods_ChatsGoTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C9A40442065D55062B9FB383 /* Pods_ChatsGoTests.framework */; };
		669993F1C6B0974F84116932 /* Pods_ChatsGo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 26769FD6CB95F37742AAA0A5 /* Pods_ChatsGo.framework */; };
		8A8146F0D53785912DC62E1E /* Pods_ChatsGoUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3D9791902FAE0058891AEF5A /* Pods_ChatsGoUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2EAED7AB2E144AC400C42455 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2EAED78C2E144AC200C42455 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2EAED7932E144AC200C42455;
			remoteInfo = ChatsGo;
		};
		2EAED7B52E144AC400C42455 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2EAED78C2E144AC200C42455 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2EAED7932E144AC200C42455;
			remoteInfo = ChatsGo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0CC1F0296A850117DC03861E /* Pods-ChatsGoTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGoTests.debug.xcconfig"; path = "Target Support Files/Pods-ChatsGoTests/Pods-ChatsGoTests.debug.xcconfig"; sourceTree = "<group>"; };
		1CB0864AF9D285B04F5F3DEF /* Pods-ChatsGoTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGoTests.release.xcconfig"; path = "Target Support Files/Pods-ChatsGoTests/Pods-ChatsGoTests.release.xcconfig"; sourceTree = "<group>"; };
		26769FD6CB95F37742AAA0A5 /* Pods_ChatsGo.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ChatsGo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		271F6DA176111FF44A9D251D /* Pods-ChatsGoUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGoUITests.release.xcconfig"; path = "Target Support Files/Pods-ChatsGoUITests/Pods-ChatsGoUITests.release.xcconfig"; sourceTree = "<group>"; };
		2EAED7942E144AC300C42455 /* ChatsGo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ChatsGo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2EAED7AA2E144AC400C42455 /* ChatsGoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChatsGoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2EAED7B42E144AC400C42455 /* ChatsGoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ChatsGoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3D9791902FAE0058891AEF5A /* Pods_ChatsGoUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ChatsGoUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		51BBE303BAAD4393B90B15F8 /* Pods-ChatsGoUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGoUITests.debug.xcconfig"; path = "Target Support Files/Pods-ChatsGoUITests/Pods-ChatsGoUITests.debug.xcconfig"; sourceTree = "<group>"; };
		7246FAC6B98C13991CF55A7D /* Pods-ChatsGo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGo.release.xcconfig"; path = "Target Support Files/Pods-ChatsGo/Pods-ChatsGo.release.xcconfig"; sourceTree = "<group>"; };
		A667EF57702FF836BED4A8AC /* Pods-ChatsGo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ChatsGo.debug.xcconfig"; path = "Target Support Files/Pods-ChatsGo/Pods-ChatsGo.debug.xcconfig"; sourceTree = "<group>"; };
		C9A40442065D55062B9FB383 /* Pods_ChatsGoTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ChatsGoTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		2EAED7BC2E144AC400C42455 /* Exceptions for "ChatsGo" folder in "ChatsGo" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Supporting/Info.plist,
			);
			target = 2EAED7932E144AC200C42455 /* ChatsGo */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2EAED7962E144AC300C42455 /* ChatsGo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				2EAED7BC2E144AC400C42455 /* Exceptions for "ChatsGo" folder in "ChatsGo" target */,
			);
			path = ChatsGo;
			sourceTree = "<group>";
		};
		2EAED7AD2E144AC400C42455 /* ChatsGoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ChatsGoTests;
			sourceTree = "<group>";
		};
		2EAED7B72E144AC400C42455 /* ChatsGoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ChatsGoUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2EAED7912E144AC200C42455 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				669993F1C6B0974F84116932 /* Pods_ChatsGo.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7A72E144AC400C42455 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1D12DDFA75CF397413E0A374 /* Pods_ChatsGoTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7B12E144AC400C42455 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A8146F0D53785912DC62E1E /* Pods_ChatsGoUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2EAED78B2E144AC200C42455 = {
			isa = PBXGroup;
			children = (
				2EAED7962E144AC300C42455 /* ChatsGo */,
				2EAED7AD2E144AC400C42455 /* ChatsGoTests */,
				2EAED7B72E144AC400C42455 /* ChatsGoUITests */,
				2EAED7952E144AC300C42455 /* Products */,
				419B808F659FFC5E090B393F /* Pods */,
				83B4F65C7DD29D8B4453B291 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2EAED7952E144AC300C42455 /* Products */ = {
			isa = PBXGroup;
			children = (
				2EAED7942E144AC300C42455 /* ChatsGo.app */,
				2EAED7AA2E144AC400C42455 /* ChatsGoTests.xctest */,
				2EAED7B42E144AC400C42455 /* ChatsGoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		419B808F659FFC5E090B393F /* Pods */ = {
			isa = PBXGroup;
			children = (
				A667EF57702FF836BED4A8AC /* Pods-ChatsGo.debug.xcconfig */,
				7246FAC6B98C13991CF55A7D /* Pods-ChatsGo.release.xcconfig */,
				0CC1F0296A850117DC03861E /* Pods-ChatsGoTests.debug.xcconfig */,
				1CB0864AF9D285B04F5F3DEF /* Pods-ChatsGoTests.release.xcconfig */,
				51BBE303BAAD4393B90B15F8 /* Pods-ChatsGoUITests.debug.xcconfig */,
				271F6DA176111FF44A9D251D /* Pods-ChatsGoUITests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		83B4F65C7DD29D8B4453B291 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				26769FD6CB95F37742AAA0A5 /* Pods_ChatsGo.framework */,
				C9A40442065D55062B9FB383 /* Pods_ChatsGoTests.framework */,
				3D9791902FAE0058891AEF5A /* Pods_ChatsGoUITests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2EAED7932E144AC200C42455 /* ChatsGo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2EAED7BD2E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGo" */;
			buildPhases = (
				5411EDD0BEF1B56C781D0C80 /* [CP] Check Pods Manifest.lock */,
				2EAED7902E144AC200C42455 /* Sources */,
				2EAED7912E144AC200C42455 /* Frameworks */,
				2EAED7922E144AC200C42455 /* Resources */,
				FD0FB75E2C99373E99AE05AC /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2EAED7962E144AC300C42455 /* ChatsGo */,
			);
			name = ChatsGo;
			productName = ChatsGo;
			productReference = 2EAED7942E144AC300C42455 /* ChatsGo.app */;
			productType = "com.apple.product-type.application";
		};
		2EAED7A92E144AC400C42455 /* ChatsGoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2EAED7C22E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGoTests" */;
			buildPhases = (
				31E5AC8F4D1E522FAE156370 /* [CP] Check Pods Manifest.lock */,
				2EAED7A62E144AC400C42455 /* Sources */,
				2EAED7A72E144AC400C42455 /* Frameworks */,
				2EAED7A82E144AC400C42455 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2EAED7AC2E144AC400C42455 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2EAED7AD2E144AC400C42455 /* ChatsGoTests */,
			);
			name = ChatsGoTests;
			productName = ChatsGoTests;
			productReference = 2EAED7AA2E144AC400C42455 /* ChatsGoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2EAED7B32E144AC400C42455 /* ChatsGoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2EAED7C52E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGoUITests" */;
			buildPhases = (
				57D75FBEEE61D652262C2141 /* [CP] Check Pods Manifest.lock */,
				2EAED7B02E144AC400C42455 /* Sources */,
				2EAED7B12E144AC400C42455 /* Frameworks */,
				2EAED7B22E144AC400C42455 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2EAED7B62E144AC400C42455 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2EAED7B72E144AC400C42455 /* ChatsGoUITests */,
			);
			name = ChatsGoUITests;
			productName = ChatsGoUITests;
			productReference = 2EAED7B42E144AC400C42455 /* ChatsGoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2EAED78C2E144AC200C42455 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					2EAED7932E144AC200C42455 = {
						CreatedOnToolsVersion = 16.4;
						LastSwiftMigration = 1640;
					};
					2EAED7A92E144AC400C42455 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2EAED7932E144AC200C42455;
					};
					2EAED7B32E144AC400C42455 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2EAED7932E144AC200C42455;
					};
				};
			};
			buildConfigurationList = 2EAED78F2E144AC200C42455 /* Build configuration list for PBXProject "ChatsGo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2EAED78B2E144AC200C42455;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2EAED7952E144AC300C42455 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2EAED7932E144AC200C42455 /* ChatsGo */,
				2EAED7A92E144AC400C42455 /* ChatsGoTests */,
				2EAED7B32E144AC400C42455 /* ChatsGoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2EAED7922E144AC200C42455 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7A82E144AC400C42455 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7B22E144AC400C42455 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		31E5AC8F4D1E522FAE156370 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ChatsGoTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5411EDD0BEF1B56C781D0C80 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ChatsGo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		57D75FBEEE61D652262C2141 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ChatsGoUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FD0FB75E2C99373E99AE05AC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ChatsGo/Pods-ChatsGo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2EAED7902E144AC200C42455 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7A62E144AC400C42455 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2EAED7B02E144AC400C42455 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2EAED7AC2E144AC400C42455 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2EAED7932E144AC200C42455 /* ChatsGo */;
			targetProxy = 2EAED7AB2E144AC400C42455 /* PBXContainerItemProxy */;
		};
		2EAED7B62E144AC400C42455 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2EAED7932E144AC200C42455 /* ChatsGo */;
			targetProxy = 2EAED7B52E144AC400C42455 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2EAED7BE2E144AC400C42455 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A667EF57702FF836BED4A8AC /* Pods-ChatsGo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SnapKit/SnapKit.framework/Headers\"",
					"$(PROJECT_DIR)/ChatsGo/Frameworks/interface",
				);
				INFOPLIST_FILE = ChatsGo/Supporting/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/ChatsGo/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "ChatsGo/Supporting/ChatsGo-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2EAED7BF2E144AC400C42455 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7246FAC6B98C13991CF55A7D /* Pods-ChatsGo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SnapKit/SnapKit.framework/Headers\"",
					"$(PROJECT_DIR)/ChatsGo/Frameworks/interface",
				);
				INFOPLIST_FILE = ChatsGo/Supporting/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/ChatsGo/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "ChatsGo/Supporting/ChatsGo-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2EAED7C02E144AC400C42455 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2EAED7C12E144AC400C42455 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2EAED7C32E144AC400C42455 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0CC1F0296A850117DC03861E /* Pods-ChatsGoTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChatsGo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChatsGo";
			};
			name = Debug;
		};
		2EAED7C42E144AC400C42455 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1CB0864AF9D285B04F5F3DEF /* Pods-ChatsGoTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ChatsGo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/ChatsGo";
			};
			name = Release;
		};
		2EAED7C62E144AC400C42455 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 51BBE303BAAD4393B90B15F8 /* Pods-ChatsGoUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = ChatsGo;
			};
			name = Debug;
		};
		2EAED7C72E144AC400C42455 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 271F6DA176111FF44A9D251D /* Pods-ChatsGoUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 3G5QLN9R2L;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.followme.ChatsGoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = ChatsGo;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2EAED78F2E144AC200C42455 /* Build configuration list for PBXProject "ChatsGo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2EAED7C02E144AC400C42455 /* Debug */,
				2EAED7C12E144AC400C42455 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2EAED7BD2E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2EAED7BE2E144AC400C42455 /* Debug */,
				2EAED7BF2E144AC400C42455 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2EAED7C22E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2EAED7C32E144AC400C42455 /* Debug */,
				2EAED7C42E144AC400C42455 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2EAED7C52E144AC400C42455 /* Build configuration list for PBXNativeTarget "ChatsGoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2EAED7C62E144AC400C42455 /* Debug */,
				2EAED7C72E144AC400C42455 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2EAED78C2E144AC200C42455 /* Project object */;
}
